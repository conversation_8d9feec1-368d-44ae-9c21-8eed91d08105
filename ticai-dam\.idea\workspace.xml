<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="39770324-d710-4974-80e5-446339dcd8e3" name="Changes" comment="feat：区域网点&#10;- 新增查询重复编码接口&#10;- 对同编号的网点进行全量更新">
      <change beforePath="$PROJECT_DIR$/src/main/resources/application-zl.yml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/resources/application-zl.yml" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_BRANCH_BY_REPOSITORY">
      <map>
        <entry key="$PROJECT_DIR$" value="feature_bug" />
      </map>
    </option>
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
    <option name="RESET_MODE" value="KEEP" />
  </component>
  <component name="GitHubPullRequestSearchHistory">{
  &quot;lastFilter&quot;: {
    &quot;state&quot;: &quot;OPEN&quot;,
    &quot;assignee&quot;: &quot;Ling90F&quot;
  }
}</component>
  <component name="GitToolBoxStore">
    <option name="recentBranches">
      <RecentBranches>
        <option name="branchesForRepo">
          <list>
            <RecentBranchesForRepo>
              <option name="branches">
                <list>
                  <RecentBranch>
                    <option name="branchName" value="staging" />
                    <option name="lastUsedInstant" value="1752552256" />
                  </RecentBranch>
                  <RecentBranch>
                    <option name="branchName" value="feature_bug" />
                    <option name="lastUsedInstant" value="1752488223" />
                  </RecentBranch>
                  <RecentBranch>
                    <option name="branchName" value="develop" />
                    <option name="lastUsedInstant" value="1752132281" />
                  </RecentBranch>
                  <RecentBranch>
                    <option name="branchName" value="release/V1.0.0" />
                    <option name="lastUsedInstant" value="**********" />
                  </RecentBranch>
                </list>
              </option>
              <option name="repositoryRootUrl" value="file://$PROJECT_DIR$" />
            </RecentBranchesForRepo>
          </list>
        </option>
      </RecentBranches>
    </option>
  </component>
  <component name="GiteeDefaultAccount">
    <option name="defaultAccountId" value="1c49213b-9ddd-40d6-bff5-d2c8da876751" />
  </component>
  <component name="GithubPullRequestsUISettings">{
  &quot;selectedUrlAndAccountId&quot;: {
    &quot;url&quot;: &quot;https://github.com/hntsz/ticai-dam.git&quot;,
    &quot;accountId&quot;: &quot;7bf87fdb-ce3c-4998-ae3a-c9a1f5b10a76&quot;
  }
}</component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="customMavenHome" value="E:\maven\apache-maven-3.8.1" />
        <option name="mavenHomeTypeForPersistence" value="CUSTOM" />
        <option name="userSettingsFile" value="E:\maven\apache-maven-3.8.1\conf\settings.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 8
}</component>
  <component name="ProjectId" id="2yyzCDvMX1G2wyKZsTKPQK2UEZL" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Maven.dam [clean].executor&quot;: &quot;Run&quot;,
    &quot;Maven.dam [install].executor&quot;: &quot;Run&quot;,
    &quot;Maven.dam [package].executor&quot;: &quot;Run&quot;,
    &quot;RequestMappingsPanelOrder0&quot;: &quot;0&quot;,
    &quot;RequestMappingsPanelOrder1&quot;: &quot;1&quot;,
    &quot;RequestMappingsPanelWidth0&quot;: &quot;75&quot;,
    &quot;RequestMappingsPanelWidth1&quot;: &quot;75&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;Spring Boot.DamApplication.executor&quot;: &quot;Run&quot;,
    &quot;git-widget-placeholder&quot;: &quot;staging&quot;,
    &quot;kotlin-language-version-configured&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;F:/work/ticai/ticai-dam&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;org.jetbrains.plugins.github.ui.GithubSettingsConfigurable&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="RunManager" selected="Spring Boot.DamApplication">
    <configuration name="SessionConfigTest.testSessionConfigLoads" type="JUnit" factoryName="JUnit" temporary="true" nameIsGenerated="true">
      <module name="dam" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.zy.config.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <option name="PACKAGE_NAME" value="com.zy.config" />
      <option name="MAIN_CLASS_NAME" value="com.zy.config.SessionConfigTest" />
      <option name="METHOD_NAME" value="testSessionConfigLoads" />
      <option name="TEST_OBJECT" value="method" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="DamApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="dam" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.zy.dam.DamApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="Dockerfile" type="docker-deploy" factoryName="dockerfile" temporary="true" server-name="Docker">
      <deployment type="dockerfile">
        <settings>
          <option name="buildOnly" value="true" />
          <option name="sourceFilePath" value="Dockerfile" />
        </settings>
      </deployment>
      <method v="2" />
    </configuration>
    <configuration default="true" type="docker-deploy" factoryName="dockerfile" temporary="true">
      <deployment type="dockerfile">
        <settings />
      </deployment>
      <method v="2" />
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="JUnit.SessionConfigTest.testSessionConfigLoads" />
        <item itemvalue="Docker.Dockerfile" />
        <item itemvalue="JUnit.SessionConfigTest.testSessionConfigLoads" />
        <item itemvalue="Docker.Dockerfile" />
        <item itemvalue="JUnit.SessionConfigTest.testSessionConfigLoads" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-fbdcb00ec9e3-intellij.indexing.shared.core-IU-251.23774.435" />
        <option value="bundled-js-predefined-d6986cc7102b-f27c65a3e318-JavaScript-IU-251.23774.435" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="39770324-d710-4974-80e5-446339dcd8e3" name="Changes" comment="" />
      <created>1750821473499</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1750821473499</updated>
      <workItem from="1750821475681" duration="4928000" />
      <workItem from="1750836501387" duration="6666000" />
      <workItem from="1750903585117" duration="2873000" />
      <workItem from="1750920005011" duration="404000" />
      <workItem from="1750922522984" duration="4890000" />
      <workItem from="1750927899241" duration="22000" />
      <workItem from="1750928277135" duration="3706000" />
      <workItem from="1750986503942" duration="15304000" />
      <workItem from="1751338868612" duration="15410000" />
      <workItem from="1751506059451" duration="4865000" />
      <workItem from="1751513538816" duration="1887000" />
      <workItem from="1751528211427" duration="8097000" />
      <workItem from="1751596858216" duration="4353000" />
      <workItem from="1751616716383" duration="5806000" />
      <workItem from="1751850683747" duration="9934000" />
      <workItem from="1751880271554" duration="3425000" />
      <workItem from="1751936865976" duration="7339000" />
      <workItem from="1751957257330" duration="11357000" />
      <workItem from="1752023595671" duration="7765000" />
      <workItem from="1752042870591" duration="9141000" />
      <workItem from="1752109769796" duration="26016000" />
      <workItem from="1752198479166" duration="693000" />
      <workItem from="1752455270860" duration="6160000" />
      <workItem from="1752475612093" duration="2299000" />
      <workItem from="1752487158925" duration="1190000" />
      <workItem from="1752541735202" duration="5728000" />
      <workItem from="1752564399789" duration="12619000" />
      <workItem from="1752627867955" duration="6274000" />
    </task>
    <task id="LOCAL-00001" summary="feat：SQL语句">
      <option name="closed" value="true" />
      <created>1751012474732</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1751012474732</updated>
    </task>
    <task id="LOCAL-00002" summary="fix：修改网点添加时间字段">
      <option name="closed" value="true" />
      <created>1751358750505</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1751358750505</updated>
    </task>
    <task id="LOCAL-00003" summary="feat：资产终端机绑定统计&#10;- 新增网点备注字段">
      <option name="closed" value="true" />
      <created>1751530294481</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1751530294481</updated>
    </task>
    <task id="LOCAL-00004" summary="fix：区域网点&#10;- 新增网点时间为location表字段">
      <option name="closed" value="true" />
      <created>1751853141103</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1751853141103</updated>
    </task>
    <task id="LOCAL-00005" summary="fix：资产终端机绑定统计&#10;- 修改网点时间为location表字段">
      <option name="closed" value="true" />
      <created>1751853466725</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1751853466725</updated>
    </task>
    <task id="LOCAL-00006" summary="feat：新增数据库字段语句">
      <option name="closed" value="true" />
      <created>1751855757973</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>1751855757973</updated>
    </task>
    <task id="LOCAL-00007" summary="feat：添加HTTP缺少安全头漏洞">
      <option name="closed" value="true" />
      <created>1751859045571</created>
      <option name="number" value="00007" />
      <option name="presentableId" value="LOCAL-00007" />
      <option name="project" value="LOCAL" />
      <updated>1751859045571</updated>
    </task>
    <task id="LOCAL-00008" summary="fix：资产终端机绑定统计&#10;- 已领用已绑定数据依赖表AM_ASSET_SN&#10;- 已领用未绑定数据依赖表AM_ASSET_CONSUMING">
      <option name="closed" value="true" />
      <created>1751968272479</created>
      <option name="number" value="00008" />
      <option name="presentableId" value="LOCAL-00008" />
      <option name="project" value="LOCAL" />
      <updated>1751968272479</updated>
    </task>
    <task id="LOCAL-00009" summary="fix：资产终端机绑定统计&#10;- 修改网点添加时间字段">
      <option name="closed" value="true" />
      <created>1752023653151</created>
      <option name="number" value="00009" />
      <option name="presentableId" value="LOCAL-00009" />
      <option name="project" value="LOCAL" />
      <updated>1752023653151</updated>
    </task>
    <task id="LOCAL-00010" summary="fix：资产终端机绑定统计&#10;- 修改网点添加时间字段">
      <option name="closed" value="true" />
      <created>1752024316997</created>
      <option name="number" value="00010" />
      <option name="presentableId" value="LOCAL-00010" />
      <option name="project" value="LOCAL" />
      <updated>1752024316997</updated>
    </task>
    <task id="LOCAL-00011" summary="fix：资产终端机绑定统计&#10;- 修改返回ID字段">
      <option name="closed" value="true" />
      <created>1752025861641</created>
      <option name="number" value="00011" />
      <option name="presentableId" value="LOCAL-00011" />
      <option name="project" value="LOCAL" />
      <updated>1752025861641</updated>
    </task>
    <task id="LOCAL-00012" summary="fix：资产终端机绑定统计&#10;- 修改筛选时间问题">
      <option name="closed" value="true" />
      <created>1752110680024</created>
      <option name="number" value="00012" />
      <option name="presentableId" value="LOCAL-00012" />
      <option name="project" value="LOCAL" />
      <updated>1752110680024</updated>
    </task>
    <task id="LOCAL-00013" summary="fix：修改MySQL语句">
      <option name="closed" value="true" />
      <created>1752118807877</created>
      <option name="number" value="00013" />
      <option name="presentableId" value="LOCAL-00013" />
      <option name="project" value="LOCAL" />
      <updated>1752118807877</updated>
    </task>
    <task id="LOCAL-00014" summary="fix：资产终端机绑定统计&#10;- 修改已领用未绑定判断表逻辑&#10;- 添加网点查询不支持部门筛选友好提示">
      <option name="closed" value="true" />
      <created>1752125946538</created>
      <option name="number" value="00014" />
      <option name="presentableId" value="LOCAL-00014" />
      <option name="project" value="LOCAL" />
      <updated>1752125946538</updated>
    </task>
    <task id="LOCAL-00015" summary="fix：MySQL语句&#10;- 批量初始化网点创建时间">
      <option name="closed" value="true" />
      <created>1752126815907</created>
      <option name="number" value="00015" />
      <option name="presentableId" value="LOCAL-00015" />
      <option name="project" value="LOCAL" />
      <updated>1752126815907</updated>
    </task>
    <task id="LOCAL-00016" summary="fix：资产终端机绑定统计&#10;- AM_ASSET_SN表存在重复数据，进行去重操作">
      <option name="closed" value="true" />
      <created>1752130603968</created>
      <option name="number" value="00016" />
      <option name="presentableId" value="LOCAL-00016" />
      <option name="project" value="LOCAL" />
      <updated>1752130603968</updated>
    </task>
    <task id="LOCAL-00017" summary="fix：区域网点&#10;- 优化数据排序根据创建时间倒叙">
      <option name="closed" value="true" />
      <created>1752132658167</created>
      <option name="number" value="00017" />
      <option name="presentableId" value="LOCAL-00017" />
      <option name="project" value="LOCAL" />
      <updated>1752132658167</updated>
    </task>
    <task id="LOCAL-00018" summary="fix：区域网点&#10;- 优化数据排序根据创建时间倒叙">
      <option name="closed" value="true" />
      <created>1752132962700</created>
      <option name="number" value="00018" />
      <option name="presentableId" value="LOCAL-00018" />
      <option name="project" value="LOCAL" />
      <updated>1752132962700</updated>
    </task>
    <task id="LOCAL-00019" summary="feat：区域网点&#10;- 批量修改">
      <option name="closed" value="true" />
      <created>1752544107699</created>
      <option name="number" value="00019" />
      <option name="presentableId" value="LOCAL-00019" />
      <option name="project" value="LOCAL" />
      <updated>1752544107699</updated>
    </task>
    <task id="LOCAL-00020" summary="feat：区域网点&#10;- 新增查询重复编码接口&#10;- 对同编号的网点进行全量更新">
      <option name="closed" value="true" />
      <created>1752634451696</created>
      <option name="number" value="00020" />
      <option name="presentableId" value="LOCAL-00020" />
      <option name="project" value="LOCAL" />
      <updated>1752634451696</updated>
    </task>
    <option name="localTasksCounter" value="21" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="RECENT_FILTERS">
      <map>
        <entry key="Branch">
          <value>
            <list>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="origin/feature_bug" />
                </option>
              </RecentGroup>
            </list>
          </value>
        </entry>
      </map>
    </option>
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="staging" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
            </State>
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="feat：SQL语句" />
    <MESSAGE value="fix：修改网点添加时间字段" />
    <MESSAGE value="feat：资产终端机绑定统计&#10;- 新增网点备注字段" />
    <MESSAGE value="fix：区域网点&#10;- 新增网点时间为location表字段" />
    <MESSAGE value="fix：资产终端机绑定统计&#10;- 修改网点时间为location表字段" />
    <MESSAGE value="feat：新增数据库字段语句" />
    <MESSAGE value="feat：添加HTTP缺少安全头漏洞" />
    <MESSAGE value="fix：资产终端机绑定统计&#10;- 已领用已绑定数据依赖表AM_ASSET_SN&#10;- 已领用未绑定数据依赖表AM_ASSET_CONSUMING" />
    <MESSAGE value="fix：资产终端机绑定统计&#10;- 修改网点添加时间字段" />
    <MESSAGE value="fix：资产终端机绑定统计&#10;- 修改返回ID字段" />
    <MESSAGE value="fix：资产终端机绑定统计&#10;- 修改筛选时间问题" />
    <MESSAGE value="fix：修改MySQL语句" />
    <MESSAGE value="fix：资产终端机绑定统计&#10;- 修改已领用未绑定判断表逻辑&#10;- 添加网点查询不支持部门筛选友好提示" />
    <MESSAGE value="fix：MySQL语句&#10;- 批量初始化网点创建时间" />
    <MESSAGE value="fix：资产终端机绑定统计&#10;- AM_ASSET_SN表存在重复数据，进行去重操作" />
    <MESSAGE value="fix：区域网点&#10;- 优化数据排序根据创建时间倒叙" />
    <MESSAGE value="feat：区域网点&#10;- 批量修改" />
    <MESSAGE value="feat：区域网点&#10;- 新增查询重复编码接口&#10;- 对同编号的网点进行全量更新" />
    <option name="LAST_COMMIT_MESSAGE" value="feat：区域网点&#10;- 新增查询重复编码接口&#10;- 对同编号的网点进行全量更新" />
  </component>
</project>