07-16 09:09:02.002 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.3.Final
07-16 09:09:02.004 [main] INFO  com.zy.dam.DamApplication - Starting DamApplication using Java 11.0.22 on bugui with PID 14488 (F:\work\ticai\ticai-dam\target\classes started by 22315 in F:\work\ticai\ticai-dam)
07-16 09:09:02.006 [main] INFO  com.zy.dam.DamApplication - The following 1 profile is active: "zl"
07-16 09:09:02.815 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
07-16 09:09:02.817 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
07-16 09:09:02.839 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 11 ms. Found 0 Redis repository interfaces.
07-16 09:09:03.084 [main] INFO  org.springframework.cloud.context.scope.GenericScope - BeanFactory id=e581c5bc-e588-3c7b-a240-b47499985897
07-16 09:09:03.443 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 20000 (http)
07-16 09:09:03.451 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-20000"]
07-16 09:09:03.451 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
07-16 09:09:03.451 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.60]
07-16 09:09:03.608 [main] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring embedded WebApplicationContext
07-16 09:09:03.608 [main] INFO  org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1564 ms
07-16 09:09:03.637 [main] INFO  com.zy.config.SessionConfig - Session Cookie配置已应用 - Profile: zl, Secure: false
07-16 09:09:03.715 [main] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Starting...
07-16 09:09:04.198 [main] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Start completed.
07-16 09:09:05.181 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - === 时区配置检查开始 ===
07-16 09:09:05.182 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - JVM默认时区: Asia/Shanghai
07-16 09:09:05.196 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - JVM时区显示名称: 中国标准时间
07-16 09:09:05.196 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - JVM时区偏移量: 8 小时
07-16 09:09:05.196 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 系统默认时区ID: Asia/Shanghai
07-16 09:09:05.196 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 当前JVM时间: 2025-07-16T09:09:05.196515700+08:00[Asia/Shanghai]
07-16 09:09:05.251 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库系统时区: UTC
07-16 09:09:05.273 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库会话时区: SYSTEM
07-16 09:09:05.293 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库全局时区: SYSTEM
07-16 09:09:05.311 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库当前时间: 2025-07-16T01:09:05
07-16 09:09:05.311 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库UTC时间: 2025-07-16T01:09:05
07-16 09:09:05.331 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - JVM时间戳: 1752628145311
07-16 09:09:05.331 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库时间戳: 1752628145000
07-16 09:09:05.331 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 时间差异: 311 毫秒
07-16 09:09:05.331 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - ✅ JVM和数据库时间基本一致
07-16 09:09:05.331 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - === 时区配置检查结束 ===
07-16 09:09:06.090 [main] INFO  org.quartz.impl.StdSchedulerFactory - Using default implementation for ThreadExecutor
07-16 09:09:06.098 [main] INFO  org.quartz.core.SchedulerSignalerImpl - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
07-16 09:09:06.098 [main] INFO  org.quartz.core.QuartzScheduler - Quartz Scheduler v.2.3.2 created.
07-16 09:09:06.099 [main] INFO  org.quartz.simpl.RAMJobStore - RAMJobStore initialized.
07-16 09:09:06.099 [main] INFO  org.quartz.core.QuartzScheduler - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

07-16 09:09:06.099 [main] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
07-16 09:09:06.099 [main] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler version: 2.3.2
07-16 09:09:06.099 [main] INFO  org.quartz.core.QuartzScheduler - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@70680f88
07-16 09:09:06.294 [main] INFO  o.s.cloud.netflix.eureka.config.DiscoveryClientOptionalArgsConfiguration - Eureka HTTP Client uses RestTemplate.
07-16 09:09:06.351 [main] WARN  o.s.c.l.config.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger - Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
07-16 09:09:06.407 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-20000"]
07-16 09:09:06.428 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 20000 (http) with context path '/api'
07-16 09:09:06.610 [main] INFO  org.springframework.scheduling.quartz.SchedulerFactoryBean - Starting Quartz Scheduler now
07-16 09:09:06.610 [main] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED started.
07-16 09:09:06.621 [main] INFO  com.zy.dam.DamApplication - Started DamApplication in 6.159 seconds (JVM running for 7.807)
07-16 09:10:00.093 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:10:00.112 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:10:00.131 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:10:00.150 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:10:19.841 [http-nio-20000-exec-1] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring DispatcherServlet 'dispatcherServlet'
07-16 09:10:19.841 [http-nio-20000-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
07-16 09:10:19.843 [http-nio-20000-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
07-16 09:10:25.836 [http-nio-20000-exec-8] WARN  org.apache.catalina.util.SessionIdGeneratorBase - Creation of SecureRandom instance for session ID generation using [SHA1PRNG] took [121] milliseconds.
07-16 09:10:25.961 [http-nio-20000-exec-8] ERROR org.springframework.web.servlet.HandlerExecutionChain - HandlerInterceptor.afterCompletion threw exception
java.lang.NullPointerException: null
	at com.zy.config.SessionConfig$CookieSecurityResponseWrapper.setHeader(SessionConfig.java:110)
	at com.zy.config.CookieSecurityInterceptor.afterCompletion(CookieSecurityInterceptor.java:32)
	at org.springframework.web.servlet.HandlerExecutionChain.triggerAfterCompletion(HandlerExecutionChain.java:178)
	at org.springframework.web.servlet.DispatcherServlet.processDispatchResult(DispatcherServlet.java:1163)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1084)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:681)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:764)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.zy.config.SecurityHeadersFilter.doFilter(SecurityHeadersFilter.java:88)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.zy.config.SessionConfig$CookieSecurityFilter.doFilter(SessionConfig.java:79)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:889)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1743)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:834)
07-16 09:10:45.029 [http-nio-20000-exec-9] WARN  org.apache.poi.util.XMLHelper - SAX Feature unsupported [log suppressed for 5 minutes]http://javax.xml.XMLConstants/property/accessExternalSchema
java.lang.IllegalArgumentException: Property 'http://javax.xml.XMLConstants/property/accessExternalSchema' is not recognized.
	at org.apache.xerces.jaxp.DocumentBuilderFactoryImpl.setAttribute(Unknown Source)
	at org.apache.poi.util.XMLHelper.trySet(XMLHelper.java:284)
	at org.apache.poi.util.XMLHelper.getDocumentBuilderFactory(XMLHelper.java:114)
	at org.apache.poi.util.XMLHelper.<clinit>(XMLHelper.java:85)
	at org.apache.poi.ooxml.util.DocumentHelper.newDocumentBuilder(DocumentHelper.java:47)
	at org.apache.poi.ooxml.util.DocumentHelper.<clinit>(DocumentHelper.java:36)
	at org.apache.poi.openxml4j.opc.internal.ContentTypeManager.save(ContentTypeManager.java:429)
	at org.apache.poi.openxml4j.opc.ZipPackage.saveImpl(ZipPackage.java:554)
	at org.apache.poi.openxml4j.opc.OPCPackage.save(OPCPackage.java:1487)
	at org.apache.poi.ooxml.POIXMLDocument.write(POIXMLDocument.java:227)
	at org.apache.poi.xssf.streaming.SXSSFWorkbook.write(SXSSFWorkbook.java:963)
	at com.alibaba.excel.context.WriteContextImpl.finish(WriteContextImpl.java:381)
	at com.alibaba.excel.write.ExcelBuilderImpl.finish(ExcelBuilderImpl.java:99)
	at com.alibaba.excel.ExcelWriter.finish(ExcelWriter.java:143)
	at com.alibaba.excel.write.builder.ExcelWriterSheetBuilder.doWrite(ExcelWriterSheetBuilder.java:63)
	at com.zy.dam.base.ctrl.AmLocationCTRL.exportBatch(AmLocationCTRL.java:109)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1067)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:681)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:764)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.zy.config.SecurityHeadersFilter.doFilter(SecurityHeadersFilter.java:88)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.zy.config.SessionConfig$CookieSecurityFilter.doFilter(SessionConfig.java:79)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:889)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1743)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:834)
07-16 09:11:00.090 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:11:00.109 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:11:00.128 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:11:00.147 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:11:25.595 [http-nio-20000-exec-8] ERROR com.zy.init.ErrorHandler - 系统内部异常
org.mybatis.spring.MyBatisSystemException: nested exception is org.apache.ibatis.exceptions.TooManyResultsException: Expected one result (or null) to be returned by selectOne(), but found: 2
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:96)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:441)
	at com.sun.proxy.$Proxy86.selectOne(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.selectOne(SqlSessionTemplate.java:160)
	at org.apache.ibatis.binding.MapperMethod.execute(MapperMethod.java:87)
	at org.apache.ibatis.binding.MapperProxy$PlainMethodInvoker.invoke(MapperProxy.java:145)
	at org.apache.ibatis.binding.MapperProxy.invoke(MapperProxy.java:86)
	at com.sun.proxy.$Proxy141.findIdByCode(Unknown Source)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:344)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:198)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:215)
	at com.sun.proxy.$Proxy142.findIdByCode(Unknown Source)
	at com.zy.dam.base.svc.AmLocationSVC.parseBatchUpdate(AmLocationSVC.java:230)
	at com.zy.dam.base.svc.AmLocationSVC$$FastClassBySpringCGLIB$$ac787214.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:783)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:753)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:753)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:698)
	at com.zy.dam.base.svc.AmLocationSVC$$EnhancerBySpringCGLIB$$21ac6b5e.parseBatchUpdate(<generated>)
	at com.zy.dam.base.ctrl.AmLocationCTRL.batchUpdate(AmLocationCTRL.java:129)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1067)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:681)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:764)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.zy.config.SecurityHeadersFilter.doFilter(SecurityHeadersFilter.java:88)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.zy.config.SessionConfig$CookieSecurityFilter.doFilter(SessionConfig.java:79)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:889)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1743)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:834)
Caused by: org.apache.ibatis.exceptions.TooManyResultsException: Expected one result (or null) to be returned by selectOne(), but found: 2
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectOne(DefaultSqlSession.java:80)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427)
	... 87 common frames omitted
07-16 09:12:00.064 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:12:00.082 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:12:00.102 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:12:00.121 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:13:00.062 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:13:00.082 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:13:00.102 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:13:00.121 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:14:00.060 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:14:00.080 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:14:00.100 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:14:00.119 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:15:00.059 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:15:00.079 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:15:00.100 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:15:00.122 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:16:00.097 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:16:00.127 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:16:00.156 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:16:00.175 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:17:00.070 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:17:00.089 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:17:00.108 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:17:00.127 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:18:00.084 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:18:00.106 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:18:00.129 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:18:00.151 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:19:00.081 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:19:00.105 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:19:00.130 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:19:00.152 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:20:00.076 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:20:00.100 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:20:00.122 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:20:00.144 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:21:00.074 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:21:00.097 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:21:00.119 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:21:00.165 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:22:00.074 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:22:00.097 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:22:00.119 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:22:00.142 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:23:00.082 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:23:00.105 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:23:00.128 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:23:00.151 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:24:00.067 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:24:00.091 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:24:00.114 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:24:00.137 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:25:00.071 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:25:00.095 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:25:00.118 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:25:00.142 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:26:00.093 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:26:00.123 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:26:00.150 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:26:00.177 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:27:00.080 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:27:00.115 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:27:00.141 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:27:00.167 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:27:23.156 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
07-16 09:27:23.310 [SpringApplicationShutdownHook] INFO  org.springframework.scheduling.quartz.SchedulerFactoryBean - Shutting down Quartz Scheduler
07-16 09:27:23.310 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
07-16 09:27:23.310 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
07-16 09:27:23.311 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
07-16 09:27:23.442 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Shutdown initiated...
07-16 09:27:23.678 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Shutdown completed.
07-16 09:27:28.440 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.3.Final
07-16 09:27:28.443 [main] INFO  com.zy.dam.DamApplication - Starting DamApplication using Java 11.0.22 on bugui with PID 30644 (F:\work\ticai\ticai-dam\target\classes started by 22315 in F:\work\ticai\ticai-dam)
07-16 09:27:28.444 [main] INFO  com.zy.dam.DamApplication - The following 1 profile is active: "zl"
07-16 09:27:29.049 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
07-16 09:27:29.050 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
07-16 09:27:29.070 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 10 ms. Found 0 Redis repository interfaces.
07-16 09:27:29.222 [main] INFO  org.springframework.cloud.context.scope.GenericScope - BeanFactory id=e581c5bc-e588-3c7b-a240-b47499985897
07-16 09:27:29.469 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 20000 (http)
07-16 09:27:29.477 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-20000"]
07-16 09:27:29.477 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
07-16 09:27:29.477 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.60]
07-16 09:27:29.600 [main] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring embedded WebApplicationContext
07-16 09:27:29.600 [main] INFO  org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1132 ms
07-16 09:27:29.629 [main] INFO  com.zy.config.SessionConfig - Session Cookie配置已应用 - Profile: zl, Secure: false
07-16 09:27:29.677 [main] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Starting...
07-16 09:27:30.156 [main] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Start completed.
07-16 09:27:30.946 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - === 时区配置检查开始 ===
07-16 09:27:30.946 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - JVM默认时区: Asia/Shanghai
07-16 09:27:30.952 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - JVM时区显示名称: 中国标准时间
07-16 09:27:30.952 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - JVM时区偏移量: 8 小时
07-16 09:27:30.952 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 系统默认时区ID: Asia/Shanghai
07-16 09:27:30.952 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 当前JVM时间: 2025-07-16T09:27:30.952734300+08:00[Asia/Shanghai]
07-16 09:27:31.022 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库系统时区: UTC
07-16 09:27:31.046 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库会话时区: SYSTEM
07-16 09:27:31.069 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库全局时区: SYSTEM
07-16 09:27:31.093 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库当前时间: 2025-07-16T01:27:31
07-16 09:27:31.093 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库UTC时间: 2025-07-16T01:27:31
07-16 09:27:31.116 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - JVM时间戳: 1752629251094
07-16 09:27:31.116 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库时间戳: 1752629251000
07-16 09:27:31.116 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 时间差异: 94 毫秒
07-16 09:27:31.116 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - ✅ JVM和数据库时间基本一致
07-16 09:27:31.116 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - === 时区配置检查结束 ===
07-16 09:27:31.755 [main] INFO  org.quartz.impl.StdSchedulerFactory - Using default implementation for ThreadExecutor
07-16 09:27:31.761 [main] INFO  org.quartz.core.SchedulerSignalerImpl - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
07-16 09:27:31.761 [main] INFO  org.quartz.core.QuartzScheduler - Quartz Scheduler v.2.3.2 created.
07-16 09:27:31.761 [main] INFO  org.quartz.simpl.RAMJobStore - RAMJobStore initialized.
07-16 09:27:31.761 [main] INFO  org.quartz.core.QuartzScheduler - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

07-16 09:27:31.762 [main] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
07-16 09:27:31.762 [main] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler version: 2.3.2
07-16 09:27:31.762 [main] INFO  org.quartz.core.QuartzScheduler - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@309d54ac
07-16 09:27:31.939 [main] INFO  o.s.cloud.netflix.eureka.config.DiscoveryClientOptionalArgsConfiguration - Eureka HTTP Client uses RestTemplate.
07-16 09:27:31.989 [main] WARN  o.s.c.l.config.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger - Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
07-16 09:27:32.040 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-20000"]
07-16 09:27:32.059 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 20000 (http) with context path '/api'
07-16 09:27:32.220 [main] INFO  org.springframework.scheduling.quartz.SchedulerFactoryBean - Starting Quartz Scheduler now
07-16 09:27:32.220 [main] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED started.
07-16 09:27:32.231 [main] INFO  com.zy.dam.DamApplication - Started DamApplication in 5.25 seconds (JVM running for 5.945)
07-16 09:27:40.732 [http-nio-20000-exec-1] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring DispatcherServlet 'dispatcherServlet'
07-16 09:27:40.732 [http-nio-20000-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
07-16 09:27:40.734 [http-nio-20000-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
07-16 09:27:47.947 [http-nio-20000-exec-7] WARN  org.apache.poi.util.XMLHelper - SAX Feature unsupported [log suppressed for 5 minutes]http://javax.xml.XMLConstants/property/accessExternalSchema
java.lang.IllegalArgumentException: Property 'http://javax.xml.XMLConstants/property/accessExternalSchema' is not recognized.
	at org.apache.xerces.jaxp.DocumentBuilderFactoryImpl.setAttribute(Unknown Source)
	at org.apache.poi.util.XMLHelper.trySet(XMLHelper.java:284)
	at org.apache.poi.util.XMLHelper.getDocumentBuilderFactory(XMLHelper.java:114)
	at org.apache.poi.util.XMLHelper.<clinit>(XMLHelper.java:85)
	at org.apache.poi.ooxml.util.DocumentHelper.newDocumentBuilder(DocumentHelper.java:47)
	at org.apache.poi.ooxml.util.DocumentHelper.<clinit>(DocumentHelper.java:36)
	at org.apache.poi.openxml4j.opc.internal.ContentTypeManager.save(ContentTypeManager.java:429)
	at org.apache.poi.openxml4j.opc.ZipPackage.saveImpl(ZipPackage.java:554)
	at org.apache.poi.openxml4j.opc.OPCPackage.save(OPCPackage.java:1487)
	at org.apache.poi.ooxml.POIXMLDocument.write(POIXMLDocument.java:227)
	at org.apache.poi.xssf.streaming.SXSSFWorkbook.write(SXSSFWorkbook.java:963)
	at com.alibaba.excel.context.WriteContextImpl.finish(WriteContextImpl.java:381)
	at com.alibaba.excel.write.ExcelBuilderImpl.finish(ExcelBuilderImpl.java:99)
	at com.alibaba.excel.ExcelWriter.finish(ExcelWriter.java:143)
	at com.alibaba.excel.write.builder.ExcelWriterSheetBuilder.doWrite(ExcelWriterSheetBuilder.java:63)
	at com.zy.dam.base.ctrl.AmLocationCTRL.exportBatch(AmLocationCTRL.java:109)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1067)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:681)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:764)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.zy.config.SecurityHeadersFilter.doFilter(SecurityHeadersFilter.java:88)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.zy.config.SessionConfig$CookieSecurityFilter.doFilter(SessionConfig.java:79)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:889)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1743)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:834)
07-16 09:28:00.087 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:28:00.110 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:28:00.133 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:28:00.156 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:28:03.776 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
07-16 09:28:03.953 [SpringApplicationShutdownHook] INFO  org.springframework.scheduling.quartz.SchedulerFactoryBean - Shutting down Quartz Scheduler
07-16 09:28:03.953 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
07-16 09:28:03.954 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
07-16 09:28:03.954 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
07-16 09:28:04.101 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Shutdown initiated...
07-16 09:28:04.348 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Shutdown completed.
07-16 09:28:07.530 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.3.Final
07-16 09:28:07.534 [main] INFO  com.zy.dam.DamApplication - Starting DamApplication using Java 11.0.22 on bugui with PID 26484 (F:\work\ticai\ticai-dam\target\classes started by 22315 in F:\work\ticai\ticai-dam)
07-16 09:28:07.535 [main] INFO  com.zy.dam.DamApplication - The following 1 profile is active: "zl"
07-16 09:28:08.090 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
07-16 09:28:08.091 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
07-16 09:28:08.109 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 10 ms. Found 0 Redis repository interfaces.
07-16 09:28:08.266 [main] INFO  org.springframework.cloud.context.scope.GenericScope - BeanFactory id=e581c5bc-e588-3c7b-a240-b47499985897
07-16 09:28:08.515 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 20000 (http)
07-16 09:28:08.522 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-20000"]
07-16 09:28:08.522 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
07-16 09:28:08.522 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.60]
07-16 09:28:08.624 [main] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring embedded WebApplicationContext
07-16 09:28:08.625 [main] INFO  org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1065 ms
07-16 09:28:08.651 [main] INFO  com.zy.config.SessionConfig - Session Cookie配置已应用 - Profile: zl, Secure: false
07-16 09:28:08.704 [main] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Starting...
07-16 09:28:09.218 [main] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Start completed.
07-16 09:28:10.002 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - === 时区配置检查开始 ===
07-16 09:28:10.003 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - JVM默认时区: Asia/Shanghai
07-16 09:28:10.008 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - JVM时区显示名称: 中国标准时间
07-16 09:28:10.008 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - JVM时区偏移量: 8 小时
07-16 09:28:10.008 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 系统默认时区ID: Asia/Shanghai
07-16 09:28:10.008 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 当前JVM时间: 2025-07-16T09:28:10.008888+08:00[Asia/Shanghai]
07-16 09:28:10.071 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库系统时区: UTC
07-16 09:28:10.097 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库会话时区: SYSTEM
07-16 09:28:10.122 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库全局时区: SYSTEM
07-16 09:28:10.148 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库当前时间: 2025-07-16T01:28:10
07-16 09:28:10.148 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库UTC时间: 2025-07-16T01:28:10
07-16 09:28:10.173 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - JVM时间戳: 1752629290148
07-16 09:28:10.173 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库时间戳: 1752629290000
07-16 09:28:10.173 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 时间差异: 148 毫秒
07-16 09:28:10.173 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - ✅ JVM和数据库时间基本一致
07-16 09:28:10.173 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - === 时区配置检查结束 ===
07-16 09:28:10.900 [main] INFO  org.quartz.impl.StdSchedulerFactory - Using default implementation for ThreadExecutor
07-16 09:28:10.905 [main] INFO  org.quartz.core.SchedulerSignalerImpl - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
07-16 09:28:10.905 [main] INFO  org.quartz.core.QuartzScheduler - Quartz Scheduler v.2.3.2 created.
07-16 09:28:10.906 [main] INFO  org.quartz.simpl.RAMJobStore - RAMJobStore initialized.
07-16 09:28:10.906 [main] INFO  org.quartz.core.QuartzScheduler - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

07-16 09:28:10.906 [main] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
07-16 09:28:10.906 [main] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler version: 2.3.2
07-16 09:28:10.906 [main] INFO  org.quartz.core.QuartzScheduler - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@441762b8
07-16 09:28:11.090 [main] INFO  o.s.cloud.netflix.eureka.config.DiscoveryClientOptionalArgsConfiguration - Eureka HTTP Client uses RestTemplate.
07-16 09:28:11.128 [main] WARN  o.s.c.l.config.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger - Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
07-16 09:28:11.168 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-20000"]
07-16 09:28:11.184 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 20000 (http) with context path '/api'
07-16 09:28:11.350 [main] INFO  org.springframework.scheduling.quartz.SchedulerFactoryBean - Starting Quartz Scheduler now
07-16 09:28:11.350 [main] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED started.
07-16 09:28:11.359 [main] INFO  com.zy.dam.DamApplication - Started DamApplication in 5.277 seconds (JVM running for 5.925)
07-16 09:28:20.646 [http-nio-20000-exec-1] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring DispatcherServlet 'dispatcherServlet'
07-16 09:28:20.647 [http-nio-20000-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
07-16 09:28:20.650 [http-nio-20000-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 3 ms
07-16 09:28:21.830 [http-nio-20000-exec-1] WARN  org.apache.poi.util.XMLHelper - SAX Feature unsupported [log suppressed for 5 minutes]http://javax.xml.XMLConstants/property/accessExternalSchema
java.lang.IllegalArgumentException: Property 'http://javax.xml.XMLConstants/property/accessExternalSchema' is not recognized.
	at org.apache.xerces.jaxp.DocumentBuilderFactoryImpl.setAttribute(Unknown Source)
	at org.apache.poi.util.XMLHelper.trySet(XMLHelper.java:284)
	at org.apache.poi.util.XMLHelper.getDocumentBuilderFactory(XMLHelper.java:114)
	at org.apache.poi.util.XMLHelper.<clinit>(XMLHelper.java:85)
	at org.apache.poi.ooxml.util.DocumentHelper.newDocumentBuilder(DocumentHelper.java:47)
	at org.apache.poi.ooxml.util.DocumentHelper.<clinit>(DocumentHelper.java:36)
	at org.apache.poi.openxml4j.opc.internal.ContentTypeManager.save(ContentTypeManager.java:429)
	at org.apache.poi.openxml4j.opc.ZipPackage.saveImpl(ZipPackage.java:554)
	at org.apache.poi.openxml4j.opc.OPCPackage.save(OPCPackage.java:1487)
	at org.apache.poi.ooxml.POIXMLDocument.write(POIXMLDocument.java:227)
	at org.apache.poi.xssf.streaming.SXSSFWorkbook.write(SXSSFWorkbook.java:963)
	at com.alibaba.excel.context.WriteContextImpl.finish(WriteContextImpl.java:381)
	at com.alibaba.excel.write.ExcelBuilderImpl.finish(ExcelBuilderImpl.java:99)
	at com.alibaba.excel.ExcelWriter.finish(ExcelWriter.java:143)
	at com.alibaba.excel.write.builder.ExcelWriterSheetBuilder.doWrite(ExcelWriterSheetBuilder.java:63)
	at com.zy.dam.base.ctrl.AmLocationCTRL.exportBatch(AmLocationCTRL.java:109)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1067)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:681)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:764)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.zy.config.SecurityHeadersFilter.doFilter(SecurityHeadersFilter.java:88)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.zy.config.SessionConfig$CookieSecurityFilter.doFilter(SessionConfig.java:79)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:889)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1743)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:834)
07-16 09:29:00.080 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:29:00.105 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:29:00.132 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:29:00.157 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:30:00.077 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:30:00.107 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:30:00.133 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:30:00.159 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:31:00.084 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:31:00.109 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:31:00.152 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:31:00.177 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:32:00.091 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:32:00.116 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:32:00.142 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:32:00.168 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:33:00.087 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:33:00.113 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:33:00.138 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:33:00.164 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:34:00.108 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:34:00.134 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:34:00.160 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:34:00.186 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:35:00.091 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:35:00.118 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:35:00.156 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:35:00.184 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:36:00.106 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:36:00.132 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:36:00.160 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:36:00.186 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:37:00.075 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:37:00.096 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:37:00.119 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:37:00.141 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:38:00.082 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:38:00.103 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:38:00.124 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:38:00.146 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:39:00.069 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:39:00.091 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:39:00.112 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:39:00.133 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:40:00.067 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:40:00.089 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:40:00.110 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:40:00.131 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:41:00.078 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:41:00.099 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:41:00.122 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:41:00.143 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:42:00.074 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:42:00.095 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:42:00.116 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:42:00.137 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:42:26.154 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
07-16 09:42:26.346 [SpringApplicationShutdownHook] INFO  org.springframework.scheduling.quartz.SchedulerFactoryBean - Shutting down Quartz Scheduler
07-16 09:42:26.347 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
07-16 09:42:26.347 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
07-16 09:42:26.347 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
07-16 09:42:26.533 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Shutdown initiated...
07-16 09:42:26.796 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Shutdown completed.
07-16 09:42:41.166 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.3.Final
07-16 09:42:41.168 [main] INFO  com.zy.dam.DamApplication - Starting DamApplication using Java 11.0.22 on bugui with PID 33528 (F:\work\ticai\ticai-dam\target\classes started by 22315 in F:\work\ticai\ticai-dam)
07-16 09:42:41.169 [main] INFO  com.zy.dam.DamApplication - The following 1 profile is active: "zl"
07-16 09:42:41.766 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
07-16 09:42:41.767 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
07-16 09:42:41.785 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 10 ms. Found 0 Redis repository interfaces.
07-16 09:42:41.939 [main] INFO  org.springframework.cloud.context.scope.GenericScope - BeanFactory id=e581c5bc-e588-3c7b-a240-b47499985897
07-16 09:42:42.179 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 20000 (http)
07-16 09:42:42.186 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-20000"]
07-16 09:42:42.186 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
07-16 09:42:42.186 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.60]
07-16 09:42:42.285 [main] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring embedded WebApplicationContext
07-16 09:42:42.285 [main] INFO  org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1091 ms
07-16 09:42:42.312 [main] INFO  com.zy.config.SessionConfig - Session Cookie配置已应用 - Profile: zl, Secure: false
07-16 09:42:42.358 [main] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Starting...
07-16 09:42:42.949 [main] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Start completed.
07-16 09:42:43.746 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - === 时区配置检查开始 ===
07-16 09:42:43.746 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - JVM默认时区: Asia/Shanghai
07-16 09:42:43.751 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - JVM时区显示名称: 中国标准时间
07-16 09:42:43.752 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - JVM时区偏移量: 8 小时
07-16 09:42:43.752 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 系统默认时区ID: Asia/Shanghai
07-16 09:42:43.752 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 当前JVM时间: 2025-07-16T09:42:43.752782900+08:00[Asia/Shanghai]
07-16 09:42:43.841 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库系统时区: UTC
07-16 09:42:43.869 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库会话时区: SYSTEM
07-16 09:42:43.894 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库全局时区: SYSTEM
07-16 09:42:43.919 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库当前时间: 2025-07-16T01:42:43
07-16 09:42:43.919 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库UTC时间: 2025-07-16T01:42:43
07-16 09:42:43.944 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - JVM时间戳: 1752630163919
07-16 09:42:43.944 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库时间戳: 1752630163000
07-16 09:42:43.944 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 时间差异: 919 毫秒
07-16 09:42:43.944 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - ✅ JVM和数据库时间基本一致
07-16 09:42:43.944 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - === 时区配置检查结束 ===
07-16 09:42:44.707 [main] INFO  org.quartz.impl.StdSchedulerFactory - Using default implementation for ThreadExecutor
07-16 09:42:44.713 [main] INFO  org.quartz.core.SchedulerSignalerImpl - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
07-16 09:42:44.713 [main] INFO  org.quartz.core.QuartzScheduler - Quartz Scheduler v.2.3.2 created.
07-16 09:42:44.713 [main] INFO  org.quartz.simpl.RAMJobStore - RAMJobStore initialized.
07-16 09:42:44.714 [main] INFO  org.quartz.core.QuartzScheduler - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

07-16 09:42:44.714 [main] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
07-16 09:42:44.714 [main] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler version: 2.3.2
07-16 09:42:44.714 [main] INFO  org.quartz.core.QuartzScheduler - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@12c30824
07-16 09:42:44.902 [main] INFO  o.s.cloud.netflix.eureka.config.DiscoveryClientOptionalArgsConfiguration - Eureka HTTP Client uses RestTemplate.
07-16 09:42:44.947 [main] WARN  o.s.c.l.config.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger - Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
07-16 09:42:45.000 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-20000"]
07-16 09:42:45.019 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 20000 (http) with context path '/api'
07-16 09:42:45.198 [main] INFO  org.springframework.scheduling.quartz.SchedulerFactoryBean - Starting Quartz Scheduler now
07-16 09:42:45.198 [main] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED started.
07-16 09:42:45.207 [main] INFO  com.zy.dam.DamApplication - Started DamApplication in 5.511 seconds (JVM running for 6.203)
07-16 09:42:51.237 [http-nio-20000-exec-1] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring DispatcherServlet 'dispatcherServlet'
07-16 09:42:51.238 [http-nio-20000-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
07-16 09:42:51.239 [http-nio-20000-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
07-16 09:43:00.088 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:43:00.114 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:43:00.139 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:43:00.164 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:44:00.083 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:44:00.109 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:44:00.135 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:44:00.160 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:45:00.115 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:45:00.142 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:45:00.166 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:45:00.191 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:45:28.379 [http-nio-20000-exec-7] WARN  org.apache.poi.util.XMLHelper - SAX Feature unsupported [log suppressed for 5 minutes]http://javax.xml.XMLConstants/property/accessExternalSchema
java.lang.IllegalArgumentException: Property 'http://javax.xml.XMLConstants/property/accessExternalSchema' is not recognized.
	at org.apache.xerces.jaxp.DocumentBuilderFactoryImpl.setAttribute(Unknown Source)
	at org.apache.poi.util.XMLHelper.trySet(XMLHelper.java:284)
	at org.apache.poi.util.XMLHelper.getDocumentBuilderFactory(XMLHelper.java:114)
	at org.apache.poi.util.XMLHelper.<clinit>(XMLHelper.java:85)
	at org.apache.poi.ooxml.util.DocumentHelper.newDocumentBuilder(DocumentHelper.java:47)
	at org.apache.poi.ooxml.util.DocumentHelper.<clinit>(DocumentHelper.java:36)
	at org.apache.poi.openxml4j.opc.internal.ContentTypeManager.parseContentTypesFile(ContentTypeManager.java:393)
	at org.apache.poi.openxml4j.opc.internal.ContentTypeManager.<init>(ContentTypeManager.java:102)
	at org.apache.poi.openxml4j.opc.internal.ZipContentTypeManager.<init>(ZipContentTypeManager.java:53)
	at org.apache.poi.openxml4j.opc.ZipPackage.getPartsImpl(ZipPackage.java:282)
	at org.apache.poi.openxml4j.opc.OPCPackage.getParts(OPCPackage.java:740)
	at org.apache.poi.openxml4j.opc.OPCPackage.open(OPCPackage.java:315)
	at org.apache.poi.ooxml.util.PackageHelper.open(PackageHelper.java:47)
	at org.apache.poi.xssf.usermodel.XSSFWorkbook.<init>(XSSFWorkbook.java:296)
	at com.zy.excel.ExcelParser.parseBytes(ExcelParser.java:85)
	at com.zy.dam.base.ctrl.AmLocationCTRL.uploadBatchFile(AmLocationCTRL.java:123)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1067)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:681)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:764)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.zy.config.SecurityHeadersFilter.doFilter(SecurityHeadersFilter.java:88)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.zy.config.SessionConfig$CookieSecurityFilter.doFilter(SessionConfig.java:79)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:889)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1743)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:834)
07-16 09:46:00.077 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:46:00.102 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:46:00.127 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:46:00.151 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:47:00.076 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:47:00.102 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:47:00.127 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:47:00.152 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:47:39.992 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
07-16 09:47:40.151 [SpringApplicationShutdownHook] INFO  org.springframework.scheduling.quartz.SchedulerFactoryBean - Shutting down Quartz Scheduler
07-16 09:47:40.151 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
07-16 09:47:40.151 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
07-16 09:47:40.151 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
07-16 09:47:40.299 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Shutdown initiated...
07-16 09:47:40.552 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Shutdown completed.
07-16 09:47:45.133 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.3.Final
07-16 09:47:45.137 [main] INFO  com.zy.dam.DamApplication - Starting DamApplication using Java 11.0.22 on bugui with PID 8088 (F:\work\ticai\ticai-dam\target\classes started by 22315 in F:\work\ticai\ticai-dam)
07-16 09:47:45.138 [main] INFO  com.zy.dam.DamApplication - The following 1 profile is active: "zl"
07-16 09:47:45.726 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
07-16 09:47:45.727 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
07-16 09:47:45.745 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 11 ms. Found 0 Redis repository interfaces.
07-16 09:47:45.900 [main] INFO  org.springframework.cloud.context.scope.GenericScope - BeanFactory id=e581c5bc-e588-3c7b-a240-b47499985897
07-16 09:47:46.134 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 20000 (http)
07-16 09:47:46.139 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-20000"]
07-16 09:47:46.139 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
07-16 09:47:46.140 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.60]
07-16 09:47:46.233 [main] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring embedded WebApplicationContext
07-16 09:47:46.234 [main] INFO  org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1070 ms
07-16 09:47:46.261 [main] INFO  com.zy.config.SessionConfig - Session Cookie配置已应用 - Profile: zl, Secure: false
07-16 09:47:46.306 [main] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Starting...
07-16 09:47:46.736 [main] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Start completed.
07-16 09:47:47.619 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - === 时区配置检查开始 ===
07-16 09:47:47.620 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - JVM默认时区: Asia/Shanghai
07-16 09:47:47.625 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - JVM时区显示名称: 中国标准时间
07-16 09:47:47.625 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - JVM时区偏移量: 8 小时
07-16 09:47:47.625 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 系统默认时区ID: Asia/Shanghai
07-16 09:47:47.626 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 当前JVM时间: 2025-07-16T09:47:47.626290900+08:00[Asia/Shanghai]
07-16 09:47:47.682 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库系统时区: UTC
07-16 09:47:47.705 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库会话时区: SYSTEM
07-16 09:47:47.732 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库全局时区: SYSTEM
07-16 09:47:47.762 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库当前时间: 2025-07-16T01:47:47
07-16 09:47:47.762 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库UTC时间: 2025-07-16T01:47:47
07-16 09:47:47.786 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - JVM时间戳: 1752630467762
07-16 09:47:47.786 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库时间戳: 1752630467000
07-16 09:47:47.786 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 时间差异: 762 毫秒
07-16 09:47:47.786 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - ✅ JVM和数据库时间基本一致
07-16 09:47:47.787 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - === 时区配置检查结束 ===
07-16 09:47:48.455 [main] INFO  org.quartz.impl.StdSchedulerFactory - Using default implementation for ThreadExecutor
07-16 09:47:48.460 [main] INFO  org.quartz.core.SchedulerSignalerImpl - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
07-16 09:47:48.460 [main] INFO  org.quartz.core.QuartzScheduler - Quartz Scheduler v.2.3.2 created.
07-16 09:47:48.460 [main] INFO  org.quartz.simpl.RAMJobStore - RAMJobStore initialized.
07-16 09:47:48.461 [main] INFO  org.quartz.core.QuartzScheduler - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

07-16 09:47:48.461 [main] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
07-16 09:47:48.461 [main] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler version: 2.3.2
07-16 09:47:48.461 [main] INFO  org.quartz.core.QuartzScheduler - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@7ff7e353
07-16 09:47:48.649 [main] INFO  o.s.cloud.netflix.eureka.config.DiscoveryClientOptionalArgsConfiguration - Eureka HTTP Client uses RestTemplate.
07-16 09:47:48.693 [main] WARN  o.s.c.l.config.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger - Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
07-16 09:47:48.759 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-20000"]
07-16 09:47:48.780 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 20000 (http) with context path '/api'
07-16 09:47:48.961 [main] INFO  org.springframework.scheduling.quartz.SchedulerFactoryBean - Starting Quartz Scheduler now
07-16 09:47:48.961 [main] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED started.
07-16 09:47:48.971 [main] INFO  com.zy.dam.DamApplication - Started DamApplication in 5.281 seconds (JVM running for 5.943)
07-16 09:47:51.034 [http-nio-20000-exec-1] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring DispatcherServlet 'dispatcherServlet'
07-16 09:47:51.034 [http-nio-20000-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
07-16 09:47:51.039 [http-nio-20000-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 5 ms
07-16 09:48:00.075 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:48:00.097 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:48:00.120 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:48:00.142 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:48:04.613 [http-nio-20000-exec-5] WARN  org.apache.poi.util.XMLHelper - SAX Feature unsupported [log suppressed for 5 minutes]http://javax.xml.XMLConstants/property/accessExternalSchema
java.lang.IllegalArgumentException: Property 'http://javax.xml.XMLConstants/property/accessExternalSchema' is not recognized.
	at org.apache.xerces.jaxp.DocumentBuilderFactoryImpl.setAttribute(Unknown Source)
	at org.apache.poi.util.XMLHelper.trySet(XMLHelper.java:284)
	at org.apache.poi.util.XMLHelper.getDocumentBuilderFactory(XMLHelper.java:114)
	at org.apache.poi.util.XMLHelper.<clinit>(XMLHelper.java:85)
	at org.apache.poi.ooxml.util.DocumentHelper.newDocumentBuilder(DocumentHelper.java:47)
	at org.apache.poi.ooxml.util.DocumentHelper.<clinit>(DocumentHelper.java:36)
	at org.apache.poi.openxml4j.opc.internal.ContentTypeManager.parseContentTypesFile(ContentTypeManager.java:393)
	at org.apache.poi.openxml4j.opc.internal.ContentTypeManager.<init>(ContentTypeManager.java:102)
	at org.apache.poi.openxml4j.opc.internal.ZipContentTypeManager.<init>(ZipContentTypeManager.java:53)
	at org.apache.poi.openxml4j.opc.ZipPackage.getPartsImpl(ZipPackage.java:282)
	at org.apache.poi.openxml4j.opc.OPCPackage.getParts(OPCPackage.java:740)
	at org.apache.poi.openxml4j.opc.OPCPackage.open(OPCPackage.java:315)
	at org.apache.poi.ooxml.util.PackageHelper.open(PackageHelper.java:47)
	at org.apache.poi.xssf.usermodel.XSSFWorkbook.<init>(XSSFWorkbook.java:296)
	at com.zy.excel.ExcelParser.parseBytes(ExcelParser.java:85)
	at com.zy.dam.base.ctrl.AmLocationCTRL.uploadBatchFile(AmLocationCTRL.java:123)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1067)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:681)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:764)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.zy.config.SecurityHeadersFilter.doFilter(SecurityHeadersFilter.java:88)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.zy.config.SessionConfig$CookieSecurityFilter.doFilter(SessionConfig.java:79)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:889)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1743)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:834)
07-16 09:48:24.693 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
07-16 09:48:24.858 [SpringApplicationShutdownHook] INFO  org.springframework.scheduling.quartz.SchedulerFactoryBean - Shutting down Quartz Scheduler
07-16 09:48:24.859 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
07-16 09:48:24.859 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
07-16 09:48:24.859 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
07-16 09:48:25.003 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Shutdown initiated...
07-16 09:48:25.263 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Shutdown completed.
07-16 09:48:28.707 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.3.Final
07-16 09:48:28.711 [main] INFO  com.zy.dam.DamApplication - Starting DamApplication using Java 11.0.22 on bugui with PID 14376 (F:\work\ticai\ticai-dam\target\classes started by 22315 in F:\work\ticai\ticai-dam)
07-16 09:48:28.712 [main] INFO  com.zy.dam.DamApplication - The following 1 profile is active: "zl"
07-16 09:48:29.359 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
07-16 09:48:29.361 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
07-16 09:48:29.378 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 11 ms. Found 0 Redis repository interfaces.
07-16 09:48:29.536 [main] INFO  org.springframework.cloud.context.scope.GenericScope - BeanFactory id=e581c5bc-e588-3c7b-a240-b47499985897
07-16 09:48:29.794 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 20000 (http)
07-16 09:48:29.800 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-20000"]
07-16 09:48:29.800 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
07-16 09:48:29.800 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.60]
07-16 09:48:29.911 [main] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring embedded WebApplicationContext
07-16 09:48:29.911 [main] INFO  org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1172 ms
07-16 09:48:29.940 [main] INFO  com.zy.config.SessionConfig - Session Cookie配置已应用 - Profile: zl, Secure: false
07-16 09:48:29.987 [main] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Starting...
07-16 09:48:30.389 [main] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Start completed.
07-16 09:48:31.269 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - === 时区配置检查开始 ===
07-16 09:48:31.269 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - JVM默认时区: Asia/Shanghai
07-16 09:48:31.274 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - JVM时区显示名称: 中国标准时间
07-16 09:48:31.274 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - JVM时区偏移量: 8 小时
07-16 09:48:31.275 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 系统默认时区ID: Asia/Shanghai
07-16 09:48:31.275 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 当前JVM时间: 2025-07-16T09:48:31.275650+08:00[Asia/Shanghai]
07-16 09:48:31.328 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库系统时区: UTC
07-16 09:48:31.350 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库会话时区: SYSTEM
07-16 09:48:31.372 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库全局时区: SYSTEM
07-16 09:48:31.395 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库当前时间: 2025-07-16T01:48:31
07-16 09:48:31.395 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库UTC时间: 2025-07-16T01:48:31
07-16 09:48:31.416 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - JVM时间戳: 1752630511395
07-16 09:48:31.416 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库时间戳: 1752630511000
07-16 09:48:31.416 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 时间差异: 395 毫秒
07-16 09:48:31.416 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - ✅ JVM和数据库时间基本一致
07-16 09:48:31.416 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - === 时区配置检查结束 ===
07-16 09:48:32.251 [main] INFO  org.quartz.impl.StdSchedulerFactory - Using default implementation for ThreadExecutor
07-16 09:48:32.257 [main] INFO  org.quartz.core.SchedulerSignalerImpl - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
07-16 09:48:32.257 [main] INFO  org.quartz.core.QuartzScheduler - Quartz Scheduler v.2.3.2 created.
07-16 09:48:32.257 [main] INFO  org.quartz.simpl.RAMJobStore - RAMJobStore initialized.
07-16 09:48:32.258 [main] INFO  org.quartz.core.QuartzScheduler - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

07-16 09:48:32.258 [main] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
07-16 09:48:32.258 [main] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler version: 2.3.2
07-16 09:48:32.258 [main] INFO  org.quartz.core.QuartzScheduler - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@12c30824
07-16 09:48:32.444 [main] INFO  o.s.cloud.netflix.eureka.config.DiscoveryClientOptionalArgsConfiguration - Eureka HTTP Client uses RestTemplate.
07-16 09:48:32.495 [main] WARN  o.s.c.l.config.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger - Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
07-16 09:48:32.544 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-20000"]
07-16 09:48:32.563 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 20000 (http) with context path '/api'
07-16 09:48:32.726 [main] INFO  org.springframework.scheduling.quartz.SchedulerFactoryBean - Starting Quartz Scheduler now
07-16 09:48:32.726 [main] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED started.
07-16 09:48:32.735 [main] INFO  com.zy.dam.DamApplication - Started DamApplication in 5.569 seconds (JVM running for 6.337)
07-16 09:48:34.660 [http-nio-20000-exec-1] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring DispatcherServlet 'dispatcherServlet'
07-16 09:48:34.660 [http-nio-20000-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
07-16 09:48:34.662 [http-nio-20000-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
07-16 09:48:35.745 [http-nio-20000-exec-1] WARN  org.apache.poi.util.XMLHelper - SAX Feature unsupported [log suppressed for 5 minutes]http://javax.xml.XMLConstants/property/accessExternalSchema
java.lang.IllegalArgumentException: Property 'http://javax.xml.XMLConstants/property/accessExternalSchema' is not recognized.
	at org.apache.xerces.jaxp.DocumentBuilderFactoryImpl.setAttribute(Unknown Source)
	at org.apache.poi.util.XMLHelper.trySet(XMLHelper.java:284)
	at org.apache.poi.util.XMLHelper.getDocumentBuilderFactory(XMLHelper.java:114)
	at org.apache.poi.util.XMLHelper.<clinit>(XMLHelper.java:85)
	at org.apache.poi.ooxml.util.DocumentHelper.newDocumentBuilder(DocumentHelper.java:47)
	at org.apache.poi.ooxml.util.DocumentHelper.<clinit>(DocumentHelper.java:36)
	at org.apache.poi.openxml4j.opc.internal.ContentTypeManager.save(ContentTypeManager.java:429)
	at org.apache.poi.openxml4j.opc.ZipPackage.saveImpl(ZipPackage.java:554)
	at org.apache.poi.openxml4j.opc.OPCPackage.save(OPCPackage.java:1487)
	at org.apache.poi.ooxml.POIXMLDocument.write(POIXMLDocument.java:227)
	at org.apache.poi.xssf.streaming.SXSSFWorkbook.write(SXSSFWorkbook.java:963)
	at com.alibaba.excel.context.WriteContextImpl.finish(WriteContextImpl.java:381)
	at com.alibaba.excel.write.ExcelBuilderImpl.finish(ExcelBuilderImpl.java:99)
	at com.alibaba.excel.ExcelWriter.finish(ExcelWriter.java:143)
	at com.alibaba.excel.write.builder.ExcelWriterSheetBuilder.doWrite(ExcelWriterSheetBuilder.java:63)
	at com.zy.dam.base.ctrl.AmLocationCTRL.exportBatch(AmLocationCTRL.java:111)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1067)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:681)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:764)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.zy.config.SecurityHeadersFilter.doFilter(SecurityHeadersFilter.java:88)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.zy.config.SessionConfig$CookieSecurityFilter.doFilter(SessionConfig.java:79)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:889)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1743)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:834)
07-16 09:49:00.089 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:49:00.111 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:49:00.134 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:49:00.159 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:50:00.067 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:50:00.093 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:50:00.115 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:50:00.138 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:51:00.077 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:51:00.102 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:51:00.124 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:51:00.145 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:52:00.074 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:52:00.096 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:52:00.130 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:52:00.153 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:53:00.069 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:53:00.090 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:53:00.116 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:53:00.138 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:54:00.075 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:54:00.103 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:54:00.125 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:54:00.147 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:54:32.647 [http-nio-20000-exec-3] WARN  org.apache.poi.util.XMLHelper - SAX Feature unsupported [log suppressed for 5 minutes]http://javax.xml.XMLConstants/property/accessExternalDTD
java.lang.IllegalArgumentException: 不支持：http://javax.xml.XMLConstants/property/accessExternalDTD
	at org.apache.xalan.processor.TransformerFactoryImpl.setAttribute(TransformerFactoryImpl.java:571)
	at org.apache.poi.util.XMLHelper.trySet(XMLHelper.java:284)
	at org.apache.poi.util.XMLHelper.getTransformerFactory(XMLHelper.java:224)
	at org.apache.poi.util.XMLHelper.newTransformer(XMLHelper.java:231)
	at org.apache.poi.openxml4j.opc.StreamHelper.saveXmlInStream(StreamHelper.java:56)
	at org.apache.poi.openxml4j.opc.internal.ZipContentTypeManager.saveImpl(ZipContentTypeManager.java:68)
	at org.apache.poi.openxml4j.opc.internal.ContentTypeManager.save(ContentTypeManager.java:450)
	at org.apache.poi.openxml4j.opc.ZipPackage.saveImpl(ZipPackage.java:554)
	at org.apache.poi.openxml4j.opc.OPCPackage.save(OPCPackage.java:1487)
	at org.apache.poi.ooxml.POIXMLDocument.write(POIXMLDocument.java:227)
	at org.apache.poi.xssf.streaming.SXSSFWorkbook.write(SXSSFWorkbook.java:963)
	at com.alibaba.excel.context.WriteContextImpl.finish(WriteContextImpl.java:381)
	at com.alibaba.excel.write.ExcelBuilderImpl.finish(ExcelBuilderImpl.java:99)
	at com.alibaba.excel.ExcelWriter.finish(ExcelWriter.java:143)
	at com.alibaba.excel.write.builder.ExcelWriterSheetBuilder.doWrite(ExcelWriterSheetBuilder.java:63)
	at com.zy.dam.base.ctrl.AmLocationCTRL.exportBatch(AmLocationCTRL.java:111)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1067)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:681)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:764)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.zy.config.SecurityHeadersFilter.doFilter(SecurityHeadersFilter.java:88)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.zy.config.SessionConfig$CookieSecurityFilter.doFilter(SessionConfig.java:79)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:889)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1743)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:834)
07-16 09:55:00.071 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:55:00.094 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:55:00.116 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:55:00.138 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:55:19.116 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
07-16 09:55:19.303 [SpringApplicationShutdownHook] INFO  org.springframework.scheduling.quartz.SchedulerFactoryBean - Shutting down Quartz Scheduler
07-16 09:55:19.304 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
07-16 09:55:19.304 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
07-16 09:55:19.304 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
07-16 09:55:19.468 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Shutdown initiated...
07-16 09:55:19.766 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Shutdown completed.
07-16 09:55:24.555 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.3.Final
07-16 09:55:24.556 [main] INFO  com.zy.dam.DamApplication - Starting DamApplication using Java 11.0.22 on bugui with PID 12236 (F:\work\ticai\ticai-dam\target\classes started by 22315 in F:\work\ticai\ticai-dam)
07-16 09:55:24.557 [main] INFO  com.zy.dam.DamApplication - The following 1 profile is active: "zl"
07-16 09:55:25.154 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
07-16 09:55:25.155 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
07-16 09:55:25.174 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 10 ms. Found 0 Redis repository interfaces.
07-16 09:55:25.341 [main] INFO  org.springframework.cloud.context.scope.GenericScope - BeanFactory id=e581c5bc-e588-3c7b-a240-b47499985897
07-16 09:55:25.574 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 20000 (http)
07-16 09:55:25.580 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-20000"]
07-16 09:55:25.581 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
07-16 09:55:25.581 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.60]
07-16 09:55:25.677 [main] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring embedded WebApplicationContext
07-16 09:55:25.677 [main] INFO  org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1094 ms
07-16 09:55:25.706 [main] INFO  com.zy.config.SessionConfig - Session Cookie配置已应用 - Profile: zl, Secure: false
07-16 09:55:25.751 [main] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Starting...
07-16 09:55:26.178 [main] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Start completed.
07-16 09:55:27.193 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - === 时区配置检查开始 ===
07-16 09:55:27.193 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - JVM默认时区: Asia/Shanghai
07-16 09:55:27.198 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - JVM时区显示名称: 中国标准时间
07-16 09:55:27.199 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - JVM时区偏移量: 8 小时
07-16 09:55:27.199 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 系统默认时区ID: Asia/Shanghai
07-16 09:55:27.199 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 当前JVM时间: 2025-07-16T09:55:27.199538+08:00[Asia/Shanghai]
07-16 09:55:27.265 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库系统时区: UTC
07-16 09:55:27.289 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库会话时区: SYSTEM
07-16 09:55:27.330 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库全局时区: SYSTEM
07-16 09:55:27.362 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库当前时间: 2025-07-16T01:55:27
07-16 09:55:27.362 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库UTC时间: 2025-07-16T01:55:27
07-16 09:55:27.396 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - JVM时间戳: 1752630927363
07-16 09:55:27.396 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库时间戳: 1752630927000
07-16 09:55:27.396 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 时间差异: 363 毫秒
07-16 09:55:27.396 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - ✅ JVM和数据库时间基本一致
07-16 09:55:27.397 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - === 时区配置检查结束 ===
07-16 09:55:28.075 [main] INFO  org.quartz.impl.StdSchedulerFactory - Using default implementation for ThreadExecutor
07-16 09:55:28.080 [main] INFO  org.quartz.core.SchedulerSignalerImpl - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
07-16 09:55:28.081 [main] INFO  org.quartz.core.QuartzScheduler - Quartz Scheduler v.2.3.2 created.
07-16 09:55:28.081 [main] INFO  org.quartz.simpl.RAMJobStore - RAMJobStore initialized.
07-16 09:55:28.081 [main] INFO  org.quartz.core.QuartzScheduler - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

07-16 09:55:28.081 [main] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
07-16 09:55:28.081 [main] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler version: 2.3.2
07-16 09:55:28.081 [main] INFO  org.quartz.core.QuartzScheduler - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@6f12fca0
07-16 09:55:28.269 [main] INFO  o.s.cloud.netflix.eureka.config.DiscoveryClientOptionalArgsConfiguration - Eureka HTTP Client uses RestTemplate.
07-16 09:55:28.357 [main] WARN  o.s.c.l.config.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger - Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
07-16 09:55:28.424 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-20000"]
07-16 09:55:28.441 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 20000 (http) with context path '/api'
07-16 09:55:28.614 [main] INFO  org.springframework.scheduling.quartz.SchedulerFactoryBean - Starting Quartz Scheduler now
07-16 09:55:28.614 [main] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED started.
07-16 09:55:28.629 [main] INFO  com.zy.dam.DamApplication - Started DamApplication in 5.554 seconds (JVM running for 6.223)
07-16 09:55:36.014 [http-nio-20000-exec-1] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring DispatcherServlet 'dispatcherServlet'
07-16 09:55:36.014 [http-nio-20000-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
07-16 09:55:36.017 [http-nio-20000-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 3 ms
07-16 09:55:37.244 [http-nio-20000-exec-1] WARN  org.apache.poi.util.XMLHelper - SAX Feature unsupported [log suppressed for 5 minutes]http://javax.xml.XMLConstants/property/accessExternalSchema
java.lang.IllegalArgumentException: Property 'http://javax.xml.XMLConstants/property/accessExternalSchema' is not recognized.
	at org.apache.xerces.jaxp.DocumentBuilderFactoryImpl.setAttribute(Unknown Source)
	at org.apache.poi.util.XMLHelper.trySet(XMLHelper.java:284)
	at org.apache.poi.util.XMLHelper.getDocumentBuilderFactory(XMLHelper.java:114)
	at org.apache.poi.util.XMLHelper.<clinit>(XMLHelper.java:85)
	at org.apache.poi.ooxml.util.DocumentHelper.newDocumentBuilder(DocumentHelper.java:47)
	at org.apache.poi.ooxml.util.DocumentHelper.<clinit>(DocumentHelper.java:36)
	at org.apache.poi.openxml4j.opc.internal.ContentTypeManager.save(ContentTypeManager.java:429)
	at org.apache.poi.openxml4j.opc.ZipPackage.saveImpl(ZipPackage.java:554)
	at org.apache.poi.openxml4j.opc.OPCPackage.save(OPCPackage.java:1487)
	at org.apache.poi.ooxml.POIXMLDocument.write(POIXMLDocument.java:227)
	at org.apache.poi.xssf.streaming.SXSSFWorkbook.write(SXSSFWorkbook.java:963)
	at com.alibaba.excel.context.WriteContextImpl.finish(WriteContextImpl.java:381)
	at com.alibaba.excel.write.ExcelBuilderImpl.finish(ExcelBuilderImpl.java:99)
	at com.alibaba.excel.ExcelWriter.finish(ExcelWriter.java:143)
	at com.alibaba.excel.write.builder.ExcelWriterSheetBuilder.doWrite(ExcelWriterSheetBuilder.java:63)
	at com.zy.dam.base.ctrl.AmLocationCTRL.exportBatch(AmLocationCTRL.java:111)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1067)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:681)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:764)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.zy.config.SecurityHeadersFilter.doFilter(SecurityHeadersFilter.java:88)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.zy.config.SessionConfig$CookieSecurityFilter.doFilter(SessionConfig.java:79)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:889)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1743)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:834)
07-16 09:56:00.096 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:56:00.122 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:56:00.148 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:56:00.172 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:57:00.097 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:57:00.122 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:57:00.163 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:57:00.200 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:58:00.094 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:58:00.118 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:58:00.142 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:58:00.191 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:59:00.075 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:59:00.099 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:59:00.123 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 09:59:00.146 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:00:00.080 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:00:00.105 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:00:00.129 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:00:00.154 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:00:31.282 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
07-16 10:00:31.455 [SpringApplicationShutdownHook] INFO  org.springframework.scheduling.quartz.SchedulerFactoryBean - Shutting down Quartz Scheduler
07-16 10:00:31.455 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
07-16 10:00:31.455 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
07-16 10:00:31.456 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
07-16 10:00:31.589 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Shutdown initiated...
07-16 10:00:31.864 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Shutdown completed.
07-16 10:00:36.428 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.3.Final
07-16 10:00:36.431 [main] INFO  com.zy.dam.DamApplication - Starting DamApplication using Java 11.0.22 on bugui with PID 30340 (F:\work\ticai\ticai-dam\target\classes started by 22315 in F:\work\ticai\ticai-dam)
07-16 10:00:36.433 [main] INFO  com.zy.dam.DamApplication - The following 1 profile is active: "zl"
07-16 10:00:37.010 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
07-16 10:00:37.011 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
07-16 10:00:37.029 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 10 ms. Found 0 Redis repository interfaces.
07-16 10:00:37.187 [main] INFO  org.springframework.cloud.context.scope.GenericScope - BeanFactory id=e581c5bc-e588-3c7b-a240-b47499985897
07-16 10:00:37.417 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 20000 (http)
07-16 10:00:37.422 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-20000"]
07-16 10:00:37.423 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
07-16 10:00:37.423 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.60]
07-16 10:00:37.516 [main] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring embedded WebApplicationContext
07-16 10:00:37.516 [main] INFO  org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1058 ms
07-16 10:00:37.542 [main] INFO  com.zy.config.SessionConfig - Session Cookie配置已应用 - Profile: zl, Secure: false
07-16 10:00:37.588 [main] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Starting...
07-16 10:00:37.997 [main] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Start completed.
07-16 10:00:38.774 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - === 时区配置检查开始 ===
07-16 10:00:38.774 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - JVM默认时区: Asia/Shanghai
07-16 10:00:38.779 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - JVM时区显示名称: 中国标准时间
07-16 10:00:38.779 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - JVM时区偏移量: 8 小时
07-16 10:00:38.780 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 系统默认时区ID: Asia/Shanghai
07-16 10:00:38.780 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 当前JVM时间: 2025-07-16T10:00:38.780096100+08:00[Asia/Shanghai]
07-16 10:00:38.835 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库系统时区: UTC
07-16 10:00:38.856 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库会话时区: SYSTEM
07-16 10:00:38.878 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库全局时区: SYSTEM
07-16 10:00:38.900 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库当前时间: 2025-07-16T02:00:38
07-16 10:00:38.900 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库UTC时间: 2025-07-16T02:00:38
07-16 10:00:38.934 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - JVM时间戳: 1752631238900
07-16 10:00:38.934 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库时间戳: 1752631238000
07-16 10:00:38.934 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 时间差异: 900 毫秒
07-16 10:00:38.934 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - ✅ JVM和数据库时间基本一致
07-16 10:00:38.934 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - === 时区配置检查结束 ===
07-16 10:00:39.569 [main] INFO  org.quartz.impl.StdSchedulerFactory - Using default implementation for ThreadExecutor
07-16 10:00:39.574 [main] INFO  org.quartz.core.SchedulerSignalerImpl - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
07-16 10:00:39.574 [main] INFO  org.quartz.core.QuartzScheduler - Quartz Scheduler v.2.3.2 created.
07-16 10:00:39.574 [main] INFO  org.quartz.simpl.RAMJobStore - RAMJobStore initialized.
07-16 10:00:39.575 [main] INFO  org.quartz.core.QuartzScheduler - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

07-16 10:00:39.575 [main] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
07-16 10:00:39.575 [main] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler version: 2.3.2
07-16 10:00:39.575 [main] INFO  org.quartz.core.QuartzScheduler - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@390978
07-16 10:00:39.749 [main] INFO  o.s.cloud.netflix.eureka.config.DiscoveryClientOptionalArgsConfiguration - Eureka HTTP Client uses RestTemplate.
07-16 10:00:39.786 [main] WARN  o.s.c.l.config.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger - Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
07-16 10:00:39.836 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-20000"]
07-16 10:00:39.854 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 20000 (http) with context path '/api'
07-16 10:00:40.007 [main] INFO  org.springframework.scheduling.quartz.SchedulerFactoryBean - Starting Quartz Scheduler now
07-16 10:00:40.008 [main] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED started.
07-16 10:00:40.017 [main] INFO  com.zy.dam.DamApplication - Started DamApplication in 5.047 seconds (JVM running for 5.7)
07-16 10:00:47.634 [http-nio-20000-exec-1] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring DispatcherServlet 'dispatcherServlet'
07-16 10:00:47.634 [http-nio-20000-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
07-16 10:00:47.636 [http-nio-20000-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
07-16 10:00:48.724 [http-nio-20000-exec-1] WARN  org.apache.poi.util.XMLHelper - SAX Feature unsupported [log suppressed for 5 minutes]http://javax.xml.XMLConstants/property/accessExternalSchema
java.lang.IllegalArgumentException: Property 'http://javax.xml.XMLConstants/property/accessExternalSchema' is not recognized.
	at org.apache.xerces.jaxp.DocumentBuilderFactoryImpl.setAttribute(Unknown Source)
	at org.apache.poi.util.XMLHelper.trySet(XMLHelper.java:284)
	at org.apache.poi.util.XMLHelper.getDocumentBuilderFactory(XMLHelper.java:114)
	at org.apache.poi.util.XMLHelper.<clinit>(XMLHelper.java:85)
	at org.apache.poi.ooxml.util.DocumentHelper.newDocumentBuilder(DocumentHelper.java:47)
	at org.apache.poi.ooxml.util.DocumentHelper.<clinit>(DocumentHelper.java:36)
	at org.apache.poi.openxml4j.opc.internal.ContentTypeManager.save(ContentTypeManager.java:429)
	at org.apache.poi.openxml4j.opc.ZipPackage.saveImpl(ZipPackage.java:554)
	at org.apache.poi.openxml4j.opc.OPCPackage.save(OPCPackage.java:1487)
	at org.apache.poi.ooxml.POIXMLDocument.write(POIXMLDocument.java:227)
	at org.apache.poi.xssf.streaming.SXSSFWorkbook.write(SXSSFWorkbook.java:963)
	at com.alibaba.excel.context.WriteContextImpl.finish(WriteContextImpl.java:381)
	at com.alibaba.excel.write.ExcelBuilderImpl.finish(ExcelBuilderImpl.java:99)
	at com.alibaba.excel.ExcelWriter.finish(ExcelWriter.java:143)
	at com.alibaba.excel.write.builder.ExcelWriterSheetBuilder.doWrite(ExcelWriterSheetBuilder.java:63)
	at com.zy.dam.base.ctrl.AmLocationCTRL.exportBatch(AmLocationCTRL.java:111)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1067)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:681)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:764)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.zy.config.SecurityHeadersFilter.doFilter(SecurityHeadersFilter.java:88)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.zy.config.SessionConfig$CookieSecurityFilter.doFilter(SessionConfig.java:79)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:889)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1743)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:834)
07-16 10:01:00.083 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:01:00.106 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:01:00.128 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:01:00.150 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:01:33.888 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
07-16 10:01:34.045 [SpringApplicationShutdownHook] INFO  org.springframework.scheduling.quartz.SchedulerFactoryBean - Shutting down Quartz Scheduler
07-16 10:01:34.045 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
07-16 10:01:34.045 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
07-16 10:01:34.046 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
07-16 10:01:34.178 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Shutdown initiated...
07-16 10:01:34.448 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Shutdown completed.
07-16 10:01:37.626 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.3.Final
07-16 10:01:37.628 [main] INFO  com.zy.dam.DamApplication - Starting DamApplication using Java 11.0.22 on bugui with PID 18444 (F:\work\ticai\ticai-dam\target\classes started by 22315 in F:\work\ticai\ticai-dam)
07-16 10:01:37.629 [main] INFO  com.zy.dam.DamApplication - The following 1 profile is active: "zl"
07-16 10:01:38.189 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
07-16 10:01:38.190 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
07-16 10:01:38.207 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 10 ms. Found 0 Redis repository interfaces.
07-16 10:01:38.365 [main] INFO  org.springframework.cloud.context.scope.GenericScope - BeanFactory id=e581c5bc-e588-3c7b-a240-b47499985897
07-16 10:01:38.592 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 20000 (http)
07-16 10:01:38.598 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-20000"]
07-16 10:01:38.599 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
07-16 10:01:38.599 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.60]
07-16 10:01:38.697 [main] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring embedded WebApplicationContext
07-16 10:01:38.697 [main] INFO  org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1044 ms
07-16 10:01:38.726 [main] INFO  com.zy.config.SessionConfig - Session Cookie配置已应用 - Profile: zl, Secure: false
07-16 10:01:38.774 [main] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Starting...
07-16 10:01:39.193 [main] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Start completed.
07-16 10:01:39.940 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - === 时区配置检查开始 ===
07-16 10:01:39.940 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - JVM默认时区: Asia/Shanghai
07-16 10:01:39.945 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - JVM时区显示名称: 中国标准时间
07-16 10:01:39.945 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - JVM时区偏移量: 8 小时
07-16 10:01:39.945 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 系统默认时区ID: Asia/Shanghai
07-16 10:01:39.945 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 当前JVM时间: 2025-07-16T10:01:39.945366500+08:00[Asia/Shanghai]
07-16 10:01:40.013 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库系统时区: UTC
07-16 10:01:40.037 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库会话时区: SYSTEM
07-16 10:01:40.060 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库全局时区: SYSTEM
07-16 10:01:40.084 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库当前时间: 2025-07-16T02:01:40
07-16 10:01:40.084 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库UTC时间: 2025-07-16T02:01:40
07-16 10:01:40.108 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - JVM时间戳: 1752631300084
07-16 10:01:40.108 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库时间戳: 1752631300000
07-16 10:01:40.108 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 时间差异: 84 毫秒
07-16 10:01:40.108 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - ✅ JVM和数据库时间基本一致
07-16 10:01:40.108 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - === 时区配置检查结束 ===
07-16 10:01:40.674 [main] INFO  org.quartz.impl.StdSchedulerFactory - Using default implementation for ThreadExecutor
07-16 10:01:40.679 [main] INFO  org.quartz.core.SchedulerSignalerImpl - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
07-16 10:01:40.679 [main] INFO  org.quartz.core.QuartzScheduler - Quartz Scheduler v.2.3.2 created.
07-16 10:01:40.679 [main] INFO  org.quartz.simpl.RAMJobStore - RAMJobStore initialized.
07-16 10:01:40.679 [main] INFO  org.quartz.core.QuartzScheduler - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

07-16 10:01:40.680 [main] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
07-16 10:01:40.680 [main] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler version: 2.3.2
07-16 10:01:40.680 [main] INFO  org.quartz.core.QuartzScheduler - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@12c30824
07-16 10:01:40.856 [main] INFO  o.s.cloud.netflix.eureka.config.DiscoveryClientOptionalArgsConfiguration - Eureka HTTP Client uses RestTemplate.
07-16 10:01:40.897 [main] WARN  o.s.c.l.config.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger - Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
07-16 10:01:40.948 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-20000"]
07-16 10:01:40.970 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 20000 (http) with context path '/api'
07-16 10:01:41.136 [main] INFO  org.springframework.scheduling.quartz.SchedulerFactoryBean - Starting Quartz Scheduler now
07-16 10:01:41.136 [main] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED started.
07-16 10:01:41.145 [main] INFO  com.zy.dam.DamApplication - Started DamApplication in 4.987 seconds (JVM running for 5.641)
07-16 10:01:55.941 [http-nio-20000-exec-1] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring DispatcherServlet 'dispatcherServlet'
07-16 10:01:55.941 [http-nio-20000-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
07-16 10:01:55.945 [http-nio-20000-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 4 ms
07-16 10:01:57.086 [http-nio-20000-exec-1] WARN  org.apache.poi.util.XMLHelper - SAX Feature unsupported [log suppressed for 5 minutes]http://javax.xml.XMLConstants/property/accessExternalSchema
java.lang.IllegalArgumentException: Property 'http://javax.xml.XMLConstants/property/accessExternalSchema' is not recognized.
	at org.apache.xerces.jaxp.DocumentBuilderFactoryImpl.setAttribute(Unknown Source)
	at org.apache.poi.util.XMLHelper.trySet(XMLHelper.java:284)
	at org.apache.poi.util.XMLHelper.getDocumentBuilderFactory(XMLHelper.java:114)
	at org.apache.poi.util.XMLHelper.<clinit>(XMLHelper.java:85)
	at org.apache.poi.ooxml.util.DocumentHelper.newDocumentBuilder(DocumentHelper.java:47)
	at org.apache.poi.ooxml.util.DocumentHelper.<clinit>(DocumentHelper.java:36)
	at org.apache.poi.openxml4j.opc.internal.ContentTypeManager.save(ContentTypeManager.java:429)
	at org.apache.poi.openxml4j.opc.ZipPackage.saveImpl(ZipPackage.java:554)
	at org.apache.poi.openxml4j.opc.OPCPackage.save(OPCPackage.java:1487)
	at org.apache.poi.ooxml.POIXMLDocument.write(POIXMLDocument.java:227)
	at org.apache.poi.xssf.streaming.SXSSFWorkbook.write(SXSSFWorkbook.java:963)
	at com.alibaba.excel.context.WriteContextImpl.finish(WriteContextImpl.java:381)
	at com.alibaba.excel.write.ExcelBuilderImpl.finish(ExcelBuilderImpl.java:99)
	at com.alibaba.excel.ExcelWriter.finish(ExcelWriter.java:143)
	at com.alibaba.excel.write.builder.ExcelWriterSheetBuilder.doWrite(ExcelWriterSheetBuilder.java:63)
	at com.zy.dam.base.ctrl.AmLocationCTRL.exportBatch(AmLocationCTRL.java:111)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1067)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:681)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:764)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.zy.config.SecurityHeadersFilter.doFilter(SecurityHeadersFilter.java:88)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.zy.config.SessionConfig$CookieSecurityFilter.doFilter(SessionConfig.java:79)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:889)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1743)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:834)
07-16 10:02:00.093 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:02:00.117 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:02:00.140 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:02:00.164 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:03:00.091 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:03:00.114 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:03:00.175 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:03:00.200 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:04:00.077 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:04:00.099 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:04:00.125 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:04:00.148 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:05:00.085 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:05:00.110 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:05:00.134 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:05:00.159 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:06:00.076 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:06:00.100 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:06:00.124 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:06:00.148 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:06:05.209 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
07-16 10:06:05.433 [SpringApplicationShutdownHook] INFO  org.springframework.scheduling.quartz.SchedulerFactoryBean - Shutting down Quartz Scheduler
07-16 10:06:05.433 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
07-16 10:06:05.433 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
07-16 10:06:05.433 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
07-16 10:06:05.569 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Shutdown initiated...
07-16 10:06:05.870 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Shutdown completed.
07-16 10:06:11.173 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.3.Final
07-16 10:06:11.174 [main] INFO  com.zy.dam.DamApplication - Starting DamApplication using Java 11.0.22 on bugui with PID 32124 (F:\work\ticai\ticai-dam\target\classes started by 22315 in F:\work\ticai\ticai-dam)
07-16 10:06:11.175 [main] INFO  com.zy.dam.DamApplication - The following 1 profile is active: "zl"
07-16 10:06:11.906 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
07-16 10:06:11.908 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
07-16 10:06:11.928 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 11 ms. Found 0 Redis repository interfaces.
07-16 10:06:12.108 [main] INFO  org.springframework.cloud.context.scope.GenericScope - BeanFactory id=e581c5bc-e588-3c7b-a240-b47499985897
07-16 10:06:12.385 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 20000 (http)
07-16 10:06:12.392 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-20000"]
07-16 10:06:12.392 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
07-16 10:06:12.392 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.60]
07-16 10:06:12.515 [main] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring embedded WebApplicationContext
07-16 10:06:12.516 [main] INFO  org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1312 ms
07-16 10:06:12.546 [main] INFO  com.zy.config.SessionConfig - Session Cookie配置已应用 - Profile: zl, Secure: false
07-16 10:06:12.603 [main] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Starting...
07-16 10:06:13.123 [main] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Start completed.
07-16 10:06:14.086 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - === 时区配置检查开始 ===
07-16 10:06:14.086 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - JVM默认时区: Asia/Shanghai
07-16 10:06:14.093 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - JVM时区显示名称: 中国标准时间
07-16 10:06:14.093 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - JVM时区偏移量: 8 小时
07-16 10:06:14.093 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 系统默认时区ID: Asia/Shanghai
07-16 10:06:14.093 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 当前JVM时间: 2025-07-16T10:06:14.093102400+08:00[Asia/Shanghai]
07-16 10:06:14.161 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库系统时区: UTC
07-16 10:06:14.187 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库会话时区: SYSTEM
07-16 10:06:14.213 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库全局时区: SYSTEM
07-16 10:06:14.240 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库当前时间: 2025-07-16T02:06:14
07-16 10:06:14.240 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库UTC时间: 2025-07-16T02:06:14
07-16 10:06:14.266 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - JVM时间戳: 1752631574240
07-16 10:06:14.266 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库时间戳: 1752631574000
07-16 10:06:14.266 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 时间差异: 240 毫秒
07-16 10:06:14.266 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - ✅ JVM和数据库时间基本一致
07-16 10:06:14.266 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - === 时区配置检查结束 ===
07-16 10:06:15.029 [main] INFO  org.quartz.impl.StdSchedulerFactory - Using default implementation for ThreadExecutor
07-16 10:06:15.034 [main] INFO  org.quartz.core.SchedulerSignalerImpl - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
07-16 10:06:15.034 [main] INFO  org.quartz.core.QuartzScheduler - Quartz Scheduler v.2.3.2 created.
07-16 10:06:15.034 [main] INFO  org.quartz.simpl.RAMJobStore - RAMJobStore initialized.
07-16 10:06:15.035 [main] INFO  org.quartz.core.QuartzScheduler - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

07-16 10:06:15.035 [main] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
07-16 10:06:15.035 [main] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler version: 2.3.2
07-16 10:06:15.035 [main] INFO  org.quartz.core.QuartzScheduler - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@2d2fe68a
07-16 10:06:15.242 [main] INFO  o.s.cloud.netflix.eureka.config.DiscoveryClientOptionalArgsConfiguration - Eureka HTTP Client uses RestTemplate.
07-16 10:06:15.323 [main] WARN  o.s.c.l.config.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger - Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
07-16 10:06:15.391 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-20000"]
07-16 10:06:15.413 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 20000 (http) with context path '/api'
07-16 10:06:15.586 [main] INFO  org.springframework.scheduling.quartz.SchedulerFactoryBean - Starting Quartz Scheduler now
07-16 10:06:15.586 [main] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED started.
07-16 10:06:15.596 [main] INFO  com.zy.dam.DamApplication - Started DamApplication in 5.992 seconds (JVM running for 6.829)
07-16 10:06:18.699 [http-nio-20000-exec-1] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring DispatcherServlet 'dispatcherServlet'
07-16 10:06:18.700 [http-nio-20000-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
07-16 10:06:18.702 [http-nio-20000-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
07-16 10:06:26.699 [http-nio-20000-exec-6] WARN  org.apache.poi.util.XMLHelper - SAX Feature unsupported [log suppressed for 5 minutes]http://javax.xml.XMLConstants/property/accessExternalSchema
java.lang.IllegalArgumentException: Property 'http://javax.xml.XMLConstants/property/accessExternalSchema' is not recognized.
	at org.apache.xerces.jaxp.DocumentBuilderFactoryImpl.setAttribute(Unknown Source)
	at org.apache.poi.util.XMLHelper.trySet(XMLHelper.java:284)
	at org.apache.poi.util.XMLHelper.getDocumentBuilderFactory(XMLHelper.java:114)
	at org.apache.poi.util.XMLHelper.<clinit>(XMLHelper.java:85)
	at org.apache.poi.ooxml.util.DocumentHelper.newDocumentBuilder(DocumentHelper.java:47)
	at org.apache.poi.ooxml.util.DocumentHelper.<clinit>(DocumentHelper.java:36)
	at org.apache.poi.openxml4j.opc.internal.ContentTypeManager.save(ContentTypeManager.java:429)
	at org.apache.poi.openxml4j.opc.ZipPackage.saveImpl(ZipPackage.java:554)
	at org.apache.poi.openxml4j.opc.OPCPackage.save(OPCPackage.java:1487)
	at org.apache.poi.ooxml.POIXMLDocument.write(POIXMLDocument.java:227)
	at org.apache.poi.xssf.streaming.SXSSFWorkbook.write(SXSSFWorkbook.java:963)
	at com.alibaba.excel.context.WriteContextImpl.finish(WriteContextImpl.java:381)
	at com.alibaba.excel.write.ExcelBuilderImpl.finish(ExcelBuilderImpl.java:99)
	at com.alibaba.excel.ExcelWriter.finish(ExcelWriter.java:143)
	at com.alibaba.excel.write.builder.ExcelWriterSheetBuilder.doWrite(ExcelWriterSheetBuilder.java:63)
	at com.zy.dam.base.ctrl.AmLocationCTRL.exportBatch(AmLocationCTRL.java:111)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1067)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:681)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:764)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.zy.config.SecurityHeadersFilter.doFilter(SecurityHeadersFilter.java:88)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.zy.config.SessionConfig$CookieSecurityFilter.doFilter(SessionConfig.java:79)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:889)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1743)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:834)
07-16 10:07:00.095 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:07:00.121 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:07:00.148 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:07:00.175 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:08:00.100 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:08:00.130 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:08:00.175 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:08:00.203 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:09:00.113 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:09:00.139 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:09:00.180 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:09:00.211 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:10:00.092 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:10:00.119 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:10:00.145 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:10:00.171 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:11:00.091 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:11:00.118 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:11:00.145 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:11:00.172 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:12:00.093 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:12:00.120 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:12:00.147 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:12:00.173 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:13:00.093 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:13:00.119 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:13:00.158 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:13:00.185 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:14:00.094 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:14:00.121 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:14:00.148 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:14:00.174 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:15:00.091 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:15:00.174 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:15:00.197 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:15:00.218 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:16:00.078 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:16:00.098 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:16:00.129 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:16:00.150 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:17:00.067 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:17:00.087 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:17:00.108 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:17:00.128 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:18:00.068 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:18:00.089 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:18:00.109 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:18:00.130 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:19:00.073 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:19:00.094 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:19:00.116 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:19:00.137 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:20:00.082 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:20:00.102 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:20:00.122 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:20:00.143 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:20:46.368 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
07-16 10:20:46.594 [SpringApplicationShutdownHook] INFO  org.springframework.scheduling.quartz.SchedulerFactoryBean - Shutting down Quartz Scheduler
07-16 10:20:46.595 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
07-16 10:20:46.595 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
07-16 10:20:46.600 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
07-16 10:20:46.731 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Shutdown initiated...
07-16 10:20:46.980 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Shutdown completed.
07-16 10:20:58.839 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.3.Final
07-16 10:20:58.842 [main] INFO  com.zy.dam.DamApplication - Starting DamApplication using Java 11.0.22 on bugui with PID 9544 (F:\work\ticai\ticai-dam\target\classes started by 22315 in F:\work\ticai\ticai-dam)
07-16 10:20:58.844 [main] INFO  com.zy.dam.DamApplication - The following 1 profile is active: "zl"
07-16 10:20:59.574 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
07-16 10:20:59.575 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
07-16 10:20:59.595 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 10 ms. Found 0 Redis repository interfaces.
07-16 10:20:59.755 [main] INFO  org.springframework.cloud.context.scope.GenericScope - BeanFactory id=e581c5bc-e588-3c7b-a240-b47499985897
07-16 10:21:00.033 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 20000 (http)
07-16 10:21:00.042 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-20000"]
07-16 10:21:00.042 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
07-16 10:21:00.042 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.60]
07-16 10:21:00.211 [main] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring embedded WebApplicationContext
07-16 10:21:00.212 [main] INFO  org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1337 ms
07-16 10:21:00.239 [main] INFO  com.zy.config.SessionConfig - Session Cookie配置已应用 - Profile: zl, Secure: false
07-16 10:21:00.285 [main] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Starting...
07-16 10:21:00.821 [main] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Start completed.
07-16 10:21:01.729 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - === 时区配置检查开始 ===
07-16 10:21:01.729 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - JVM默认时区: Asia/Shanghai
07-16 10:21:01.735 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - JVM时区显示名称: 中国标准时间
07-16 10:21:01.735 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - JVM时区偏移量: 8 小时
07-16 10:21:01.736 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 系统默认时区ID: Asia/Shanghai
07-16 10:21:01.736 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 当前JVM时间: 2025-07-16T10:21:01.736202400+08:00[Asia/Shanghai]
07-16 10:21:01.802 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库系统时区: UTC
07-16 10:21:01.828 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库会话时区: SYSTEM
07-16 10:21:01.854 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库全局时区: SYSTEM
07-16 10:21:01.882 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库当前时间: 2025-07-16T02:21:01
07-16 10:21:01.882 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库UTC时间: 2025-07-16T02:21:01
07-16 10:21:01.908 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - JVM时间戳: 1752632461882
07-16 10:21:01.908 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库时间戳: 1752632461000
07-16 10:21:01.908 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 时间差异: 882 毫秒
07-16 10:21:01.908 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - ✅ JVM和数据库时间基本一致
07-16 10:21:01.908 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - === 时区配置检查结束 ===
07-16 10:21:02.614 [main] INFO  org.quartz.impl.StdSchedulerFactory - Using default implementation for ThreadExecutor
07-16 10:21:02.620 [main] INFO  org.quartz.core.SchedulerSignalerImpl - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
07-16 10:21:02.620 [main] INFO  org.quartz.core.QuartzScheduler - Quartz Scheduler v.2.3.2 created.
07-16 10:21:02.620 [main] INFO  org.quartz.simpl.RAMJobStore - RAMJobStore initialized.
07-16 10:21:02.621 [main] INFO  org.quartz.core.QuartzScheduler - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

07-16 10:21:02.621 [main] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
07-16 10:21:02.621 [main] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler version: 2.3.2
07-16 10:21:02.621 [main] INFO  org.quartz.core.QuartzScheduler - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@df04d12
07-16 10:21:02.820 [main] INFO  o.s.cloud.netflix.eureka.config.DiscoveryClientOptionalArgsConfiguration - Eureka HTTP Client uses RestTemplate.
07-16 10:21:02.871 [main] WARN  o.s.c.l.config.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger - Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
07-16 10:21:02.940 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-20000"]
07-16 10:21:02.961 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 20000 (http) with context path '/api'
07-16 10:21:03.139 [main] INFO  org.springframework.scheduling.quartz.SchedulerFactoryBean - Starting Quartz Scheduler now
07-16 10:21:03.139 [main] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED started.
07-16 10:21:03.149 [main] INFO  com.zy.dam.DamApplication - Started DamApplication in 5.786 seconds (JVM running for 6.852)
07-16 10:21:10.264 [http-nio-20000-exec-1] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring DispatcherServlet 'dispatcherServlet'
07-16 10:21:10.264 [http-nio-20000-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
07-16 10:21:10.268 [http-nio-20000-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 4 ms
07-16 10:21:13.749 [http-nio-20000-exec-5] WARN  org.apache.poi.util.XMLHelper - SAX Feature unsupported [log suppressed for 5 minutes]http://javax.xml.XMLConstants/property/accessExternalSchema
java.lang.IllegalArgumentException: Property 'http://javax.xml.XMLConstants/property/accessExternalSchema' is not recognized.
	at org.apache.xerces.jaxp.DocumentBuilderFactoryImpl.setAttribute(Unknown Source)
	at org.apache.poi.util.XMLHelper.trySet(XMLHelper.java:284)
	at org.apache.poi.util.XMLHelper.getDocumentBuilderFactory(XMLHelper.java:114)
	at org.apache.poi.util.XMLHelper.<clinit>(XMLHelper.java:85)
	at org.apache.poi.ooxml.util.DocumentHelper.newDocumentBuilder(DocumentHelper.java:47)
	at org.apache.poi.ooxml.util.DocumentHelper.<clinit>(DocumentHelper.java:36)
	at org.apache.poi.openxml4j.opc.internal.ContentTypeManager.save(ContentTypeManager.java:429)
	at org.apache.poi.openxml4j.opc.ZipPackage.saveImpl(ZipPackage.java:554)
	at org.apache.poi.openxml4j.opc.OPCPackage.save(OPCPackage.java:1487)
	at org.apache.poi.ooxml.POIXMLDocument.write(POIXMLDocument.java:227)
	at org.apache.poi.xssf.streaming.SXSSFWorkbook.write(SXSSFWorkbook.java:963)
	at com.alibaba.excel.context.WriteContextImpl.finish(WriteContextImpl.java:381)
	at com.alibaba.excel.write.ExcelBuilderImpl.finish(ExcelBuilderImpl.java:99)
	at com.alibaba.excel.ExcelWriter.finish(ExcelWriter.java:143)
	at com.alibaba.excel.write.builder.ExcelWriterSheetBuilder.doWrite(ExcelWriterSheetBuilder.java:63)
	at com.zy.dam.base.ctrl.AmLocationCTRL.exportBatch(AmLocationCTRL.java:109)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1067)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:681)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:764)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.zy.config.SecurityHeadersFilter.doFilter(SecurityHeadersFilter.java:88)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.zy.config.SessionConfig$CookieSecurityFilter.doFilter(SessionConfig.java:79)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:889)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1743)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:834)
07-16 10:22:00.106 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:22:00.132 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:22:00.158 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:22:00.185 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:23:00.082 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:23:00.108 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:23:00.135 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:23:00.162 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:24:00.079 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:24:00.106 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:24:00.132 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:24:00.158 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:25:00.063 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:25:00.089 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:25:00.153 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:25:00.180 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:26:00.083 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:26:00.108 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:26:00.135 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:26:00.160 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:27:00.083 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:27:00.109 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:27:00.134 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:27:00.159 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:28:00.084 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:28:00.110 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:28:00.135 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:28:00.161 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:29:00.078 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:29:00.103 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:29:00.129 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:29:00.154 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:30:00.092 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:30:00.116 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:30:00.164 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:30:00.191 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:31:00.073 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:31:00.094 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:31:00.115 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:31:00.135 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:31:55.143 [http-nio-20000-exec-8] WARN  org.apache.poi.util.XMLHelper - SAX Feature unsupported [log suppressed for 5 minutes]http://javax.xml.XMLConstants/property/accessExternalDTD
java.lang.IllegalArgumentException: 不支持：http://javax.xml.XMLConstants/property/accessExternalDTD
	at org.apache.xalan.processor.TransformerFactoryImpl.setAttribute(TransformerFactoryImpl.java:571)
	at org.apache.poi.util.XMLHelper.trySet(XMLHelper.java:284)
	at org.apache.poi.util.XMLHelper.getTransformerFactory(XMLHelper.java:224)
	at org.apache.poi.util.XMLHelper.newTransformer(XMLHelper.java:231)
	at org.apache.poi.openxml4j.opc.StreamHelper.saveXmlInStream(StreamHelper.java:56)
	at org.apache.poi.openxml4j.opc.internal.ZipContentTypeManager.saveImpl(ZipContentTypeManager.java:68)
	at org.apache.poi.openxml4j.opc.internal.ContentTypeManager.save(ContentTypeManager.java:450)
	at org.apache.poi.openxml4j.opc.ZipPackage.saveImpl(ZipPackage.java:554)
	at org.apache.poi.openxml4j.opc.OPCPackage.save(OPCPackage.java:1487)
	at org.apache.poi.ooxml.POIXMLDocument.write(POIXMLDocument.java:227)
	at org.apache.poi.xssf.streaming.SXSSFWorkbook.write(SXSSFWorkbook.java:963)
	at com.alibaba.excel.context.WriteContextImpl.finish(WriteContextImpl.java:381)
	at com.alibaba.excel.write.ExcelBuilderImpl.finish(ExcelBuilderImpl.java:99)
	at com.alibaba.excel.ExcelWriter.finish(ExcelWriter.java:143)
	at com.alibaba.excel.write.builder.ExcelWriterSheetBuilder.doWrite(ExcelWriterSheetBuilder.java:63)
	at com.zy.dam.base.ctrl.AmLocationCTRL.exportBatch(AmLocationCTRL.java:109)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1067)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:681)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:764)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.zy.config.SecurityHeadersFilter.doFilter(SecurityHeadersFilter.java:88)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.zy.config.SessionConfig$CookieSecurityFilter.doFilter(SessionConfig.java:79)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:889)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1743)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:834)
07-16 10:32:00.072 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:32:00.092 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:32:00.113 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:32:00.143 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:33:00.080 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:33:00.101 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:33:00.122 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:33:00.143 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:34:00.070 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:34:00.090 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:34:00.111 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:34:00.133 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:35:00.075 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:35:00.096 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:35:00.117 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:35:00.138 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:36:00.072 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:36:00.095 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:36:00.116 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:36:00.137 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:37:00.075 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:37:00.096 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:37:00.117 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:37:00.137 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:38:00.078 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:38:00.100 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:38:00.126 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:38:00.147 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:39:00.088 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:39:00.091 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
07-16 10:39:00.110 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:39:00.133 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:39:00.154 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-16 10:39:00.254 [SpringApplicationShutdownHook] INFO  org.springframework.scheduling.quartz.SchedulerFactoryBean - Shutting down Quartz Scheduler
07-16 10:39:00.254 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
07-16 10:39:00.254 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
07-16 10:39:00.254 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
07-16 10:39:00.412 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Shutdown initiated...
07-16 10:39:00.662 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Shutdown completed.
