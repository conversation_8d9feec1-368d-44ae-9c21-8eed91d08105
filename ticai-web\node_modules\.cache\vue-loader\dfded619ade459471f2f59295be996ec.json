{"remainingRequest": "F:\\work\\ticai\\ticai-web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!F:\\work\\ticai\\ticai-web\\src\\views\\bd\\region\\index.vue?vue&type=template&id=51f74d7a", "dependencies": [{"path": "F:\\work\\ticai\\ticai-web\\src\\views\\bd\\region\\index.vue", "mtime": 1752634123882}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1747730941451}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1747730939047}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1747730941451}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1747730939034}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}