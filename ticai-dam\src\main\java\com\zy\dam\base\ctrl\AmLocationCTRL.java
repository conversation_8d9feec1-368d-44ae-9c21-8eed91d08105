package com.zy.dam.base.ctrl;

import com.zy.app.vo.User;
import com.zy.dam.base.orm.AmLocation;
import com.zy.dam.base.req.form.AmLocationForm;
import com.zy.dam.base.req.page.AmLocationPage;
import com.zy.dam.base.svc.AmLocationSVC;
import com.zy.dam.base.vo.AmLocationVo;
import com.zy.dam.base.vo.LocationRow;
import com.zy.dam.base.vo.LocationBatchUpdateRow;
import com.zy.dam.base.vo.LocationBatchUpdateExportVo;
import com.zy.excel.ExcelParser;
import com.zy.model.*;
import com.zy.sys.svc.SysFileSVC;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

import com.alibaba.excel.EasyExcel;

@Tag(name = "资管-地点")
@RestController
@RequestMapping("/am/location")
public class AmLocationCTRL {

    @Resource
    private AmLocationSVC svc;

    @Resource
    private SysFileSVC fileSVC;

    @Operation(summary = "分页查询")
    @PostMapping("/page")
    public PageR page(@Parameter(description = "分页条件") @RequestBody AmLocationPage page) {
        svc.page(page);
        return page.getR();
    }

    @Operation(summary = "保存")
    @PostMapping("/save")
    public Result save(@Validated @Parameter(description = "数据") @RequestBody AmLocation item) {
        return svc.save(item);
    }

    @Operation(summary = "删除")
    @PostMapping("/delete/{id}")
    public Result delete(@Parameter(description = "要删除的ID") @PathVariable("id") String id) {
        svc.delete(id);
        return Result.ok();
    }

    @Operation(summary = "获取区域网点")
    @PostMapping("/region/{id}")
    public DataResult<List<AmLocation>> region(@Parameter(description = "要删除的ID") @PathVariable("id") String id) {
        return DataResult.data(svc.findByRegion(id));
    }

    @Operation(summary = "获取区域网点级联")
    @PostMapping("/cascade")
    public List<VueTreeNode> cascade() {
        return svc.findCascade();
    }

    @Operation(summary = "上传文件")
    @PostMapping("/uploadFile")
    public DataResult<List<LocationRow>> uploadFile(@Parameter(description = "文件ID") @RequestBody VueFile file) {
        if (file.getId() == null) return DataResult.err("无效的参数");
        byte[] fileData = fileSVC.download(file.getId());
        if (fileData == null || fileData.length == 0) return DataResult.err("无法获取文件内容");
        List<LocationRow> list = ExcelParser.parseBytes(fileData, LocationRow.class, "xls".equalsIgnoreCase(file.getExt()));
        return DataResult.data(list);
    }

    @Operation(summary = "上传数据")
    @PostMapping("/uploadData")
    public DataResult<List<LocationRow>> uploadFile(@Parameter(description = "数据") @RequestBody List<LocationRow> data, @Parameter(hidden = true) User user) {
        if (data.isEmpty()) return DataResult.err("无任务数据");
        int ret = svc.parseUpload(data, user.getId());
        if (ret > 0) return new DataResult<>(2, data);
        return new DataResult<>(1);
    }

    @Operation(summary = "保存网点设备")
    @PostMapping("/saveDevice")
    public Result saveDevice(@Validated @Parameter(description = "数据") @RequestBody AmLocationForm form) {
        return svc.saveDevice(form);
    }

    @Operation(summary = "获取网点设备")
    @PostMapping("/get/{id}")
    public DataResult<AmLocationVo> get(@Parameter(description = "要删除的ID") @PathVariable("id") String id) {
        return DataResult.data(svc.findVo(id));
    }

    @Operation(summary = "导出网点批量修改模板")
    @PostMapping("/exportBatch/{regionId}")
    public void exportBatch(@Parameter(description = "区域ID") @PathVariable("regionId") String regionId,
                           @Parameter(hidden = true) HttpServletResponse response) {
        try {
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setHeader("Content-Disposition", "attachment; filename=location_batch_update.xlsx");

            var vos = svc.findBatchUpdateExport(regionId);
            EasyExcel.write(response.getOutputStream(), LocationBatchUpdateExportVo.class).sheet("网点批量修改").doWrite(vos);
        } catch (Exception e) {
            throw new RuntimeException("导出网点批量修改模板出错", e);
        }
    }

    @Operation(summary = "上传批量更新文件")
    @PostMapping("/uploadBatchFile")
    public DataResult<List<LocationBatchUpdateRow>> uploadBatchFile(@Parameter(description = "文件ID") @RequestBody VueFile file) {
        if (file.getId() == null) return DataResult.err("无效的参数");
        byte[] fileData = fileSVC.download(file.getId());
        if (fileData == null || fileData.length == 0) return DataResult.err("无法获取文件内容");
        List<LocationBatchUpdateRow> list = ExcelParser.parseBytes(fileData, LocationBatchUpdateRow.class, "xls".equalsIgnoreCase(file.getExt()));
        return DataResult.data(list);
    }

    @Operation(summary = "检查批量更新数据中的重复编码")
    @PostMapping("/checkBatchDuplicates")
    public DataResult<Map<String, Object>> checkBatchDuplicates(@Parameter(description = "数据") @RequestBody List<LocationBatchUpdateRow> data) {
        if (data.isEmpty()) return DataResult.err("无任务数据");
        Map<String, Object> result = svc.checkBatchUpdateDuplicates(data);
        return DataResult.data(result);
    }

    @Operation(summary = "批量更新网点信息")
    @PostMapping("/batchUpdate")
    public DataResult<List<LocationBatchUpdateRow>> batchUpdate(@Parameter(description = "数据") @RequestBody List<LocationBatchUpdateRow> data, @Parameter(hidden = true) User user) {
        if (data.isEmpty()) return DataResult.err("无任务数据");
        int ret = svc.parseBatchUpdate(data, user.getId());
        if (ret > 0) return new DataResult<>(2, data);
        return new DataResult<>(1);
    }
}
