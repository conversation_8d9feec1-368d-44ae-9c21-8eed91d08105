package com.zy.dam.base.svc;

import com.zy.dam.base.dao.AmLocationDAO;
import com.zy.dam.base.orm.AmLocation;
import com.zy.dam.base.orm.AmLocationAsset;
import com.zy.dam.base.req.form.AmLocationForm;
import com.zy.dam.base.vo.AmLocationVo;
import com.zy.dam.base.vo.LocationRow;
import com.zy.dam.base.vo.LocationBatchUpdateRow;
import com.zy.dam.base.vo.LocationBatchUpdateExportVo;
import com.zy.dam.base.vo.LocationVo;
import com.zy.model.Page;
import com.zy.model.Result;
import com.zy.model.VueTreeNode;
import com.zy.util.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;

/**
 * <p>
 * 资管-地点业务服务接口
 * </p>
 */
@Service
public class AmLocationSVC {

    /**
     * 资管-地点数据访问接口
     **/
    @Resource
    private AmLocationDAO dao;

    public Result save(AmLocation item) {
        String id = dao.findIdByCode(item.getCode());
        if (StringUtils.isEmpty(item.getId())) {
            if (id != null)
                return Result.err("该编码已经存在");
            // 设置创建时间
            item.setCreateTime(new Date());
            dao.insert(item);
        } else {
            if (id != null && !id.equals(item.getId()))
                return Result.err("该编码已经存在");
            dao.update(item);
        }
        return Result.ok();
    }

    public List<LocationVo> page(Page page) {
        return dao.page(page);
    }

    public void delete(String id) {
        dao.delete(id);
        dao.deleteItem(id);
    }

    public List<AmLocation> findByRegion(String id) {
        return dao.findByRegion(id);
    }

    public List<VueTreeNode> findCascade() {
        List<VueTreeNode> roots = dao.listRegion();
        Map<String, VueTreeNode> map = new HashMap<>();
        for (VueTreeNode vt : roots)
            map.put(vt.getId(), vt);
        VueTreeNode root;
        VueTreeNode node;
        for (VueTreeNode item : dao.listLocation()) {
            root = map.get(item.getPid());
            if (root != null)
                root.addChild(item);
        }
        return roots;
    }

    @Transactional(rollbackFor = Exception.class)
    public int parseUpload(List<LocationRow> rows, String user) {
        String v;
        int count = 0;
        boolean err;
        Set<String> noSet = new HashSet<>();
        for (LocationRow row : rows) {
            err = false;
            row.rowMsg = null;
            if (StringUtils.isEmpty(row.region)) { // 所属市县编号
                row.appendMsg("所属市县编号不能为空");
                err = true;
            }
            if (StringUtils.isEmpty(row.sn)) { // 销售终端编号
                row.appendMsg("销售终端编号不能为空");
                err = true;
            } else if (dao.findIdBySn(row.sn) != null) {
                row.appendMsg("销售终端编号已经存在");
                err = true;
            } else if (noSet.contains(row.sn)) {
                row.appendMsg("销售终端编号在本次导入中重复");
                err = true;
            } else {
                noSet.add(row.sn);
            }
            if (StringUtils.isEmpty(row.code)) { // 网点编号
                row.appendMsg("网点编号不能为空");
                err = true;
            }
            // else if (dao.findLocationByCode(row.code) != null) {
            // row.appendMsg("网点编号已经存在");
            // err = true;
            // } else if (noSet.contains(row.code)) {
            // row.appendMsg("网点编号在本次导入中重复");
            // err = true;
            // }
            else {
                noSet.add(row.code);
            }
            if (StringUtils.isEmpty(row.name)) { // 业主姓名
                row.appendMsg("业主姓名不能为空");
                err = true;
            }
            if (err)
                count++;
        }
        if (count == 0) { // 导入数据
            AmLocation amLocation;
            AmLocationAsset amLocationAsset;
            for (LocationRow row : rows) {
                amLocation = new AmLocation();
                amLocationAsset = new AmLocationAsset();
                BeanUtils.copyProperties(row, amLocation);
                amLocation.setMemo("批量导入");
                dao.insert(amLocation);
                amLocationAsset.setLocation(amLocation.getId());
                amLocationAsset.setSn(row.getSn());
                // 设置创建时间
                amLocationAsset.setTime(new Date());
                // 设置操作人
                amLocationAsset.setUser(user);
                dao.insertItem(amLocationAsset);
            }
        }
        return count;
    }

    @Transactional(rollbackFor = Exception.class)
    public Result saveDevice(AmLocationForm form) {
        String id = dao.findIdByCode(form.getCode());
        AmLocation item = new AmLocation();
        BeanUtils.copyProperties(form, item);
        if (StringUtils.isEmpty(item.getId())) {
            if (id != null)
                return Result.err("该编码已经存在");
            // 设置创建时间
            item.setCreateTime(new Date());
            dao.insert(item);
        } else {
            if (id != null && !id.equals(item.getId()))
                return Result.err("该编码已经存在");
            dao.update(item);
            dao.deleteItem(item.getId());
        }
        if (form.getAmLocationAsset() != null) {
            for (AmLocationAsset amLocationAsset : form.getAmLocationAsset()) {
                String $id = dao.findIdBySn(amLocationAsset.getSn());
                if ($id != null)
                    return Result.err("该终端号已经存在");
                if (amLocationAsset.getSn() == null)
                    return Result.err("该终端号不能为空");
                amLocationAsset.setLocation(item.getId());
                // 设置创建时间
                amLocationAsset.setTime(new Date());
                // 设置操作人
                amLocationAsset.setUser(form.getUser());
                dao.insertItem(amLocationAsset);
            }
        }
        return Result.ok();
    }

    public AmLocationVo findVo(String id) {
        AmLocationVo vo = dao.findVo(id);
        if (vo == null)
            return null;
        vo.setAmLocationAsset(dao.findLocationAsset(id));
        return vo;
    }

    /**
     * 根据区域ID导出网点批量修改数据
     */
    public List<LocationBatchUpdateExportVo> findBatchUpdateExport(String regionId) {
        List<AmLocation> locations = dao.findByRegion(regionId);
        List<LocationBatchUpdateExportVo> result = new ArrayList<>();
        Map<String, LocationBatchUpdateExportVo> codeMap = new LinkedHashMap<>();

        for (AmLocation location : locations) {
            LocationBatchUpdateExportVo vo = new LocationBatchUpdateExportVo();
            vo.setCode(location.getCode());
            vo.setName(location.getName());
            vo.setMemo(location.getMemo());
            codeMap.put(location.getCode(), vo);
        }

        result.addAll(codeMap.values());
        return result;
    }

    /**
     * 检查批量更新数据中是否存在重复编码（包括导入数据内部重复和数据库重复）
     */
    public Map<String, Object> checkBatchUpdateDuplicates(List<LocationBatchUpdateRow> rows) {
        Map<String, Object> result = new HashMap<>();
        List<String> importDuplicateCodes = new ArrayList<>();
        List<String> dbDuplicateCodes = new ArrayList<>();
        Map<String, Integer> codeCountMap = new HashMap<>();

        // 统计导入数据中每个编码出现的次数
        for (LocationBatchUpdateRow row : rows) {
            if (!StringUtils.isEmpty(row.code)) {
                codeCountMap.put(row.code, codeCountMap.getOrDefault(row.code, 0) + 1);
            }
        }

        // 找出导入数据中重复的编码
        for (Map.Entry<String, Integer> entry : codeCountMap.entrySet()) {
            if (entry.getValue() > 1) {
                importDuplicateCodes.add(entry.getKey());
            }
        }

        // 检查数据库中是否存在重复编码
        Set<String> checkedCodes = new HashSet<>();
        for (LocationBatchUpdateRow row : rows) {
            if (!StringUtils.isEmpty(row.code) && !checkedCodes.contains(row.code)) {
                checkedCodes.add(row.code);
                try {
                    dao.findIdByCode(row.code);
                } catch (Exception e) {
                    dbDuplicateCodes.add(row.code);
                }
            }
        }

        // 合并所有需要特殊处理的编码
        Set<String> allDuplicateCodes = new HashSet<>();
        allDuplicateCodes.addAll(importDuplicateCodes);
        allDuplicateCodes.addAll(dbDuplicateCodes);

        result.put("hasDuplicates", !allDuplicateCodes.isEmpty());
        result.put("duplicateCodes", new ArrayList<>(allDuplicateCodes));
        result.put("duplicateCount", allDuplicateCodes.size());
        result.put("importDuplicates", importDuplicateCodes);
        result.put("dbDuplicates", dbDuplicateCodes);

        return result;
    }

    /**
     * 批量更新网点信息
     * @param rows 更新数据
     * @param user 操作用户
     */
    @Transactional(rollbackFor = Exception.class)
    public int parseBatchUpdate(List<LocationBatchUpdateRow> rows, String user) {
        int count = 0;
        boolean err;
        Map<String, LocationBatchUpdateRow> codeMap = new LinkedHashMap<>();

        for (LocationBatchUpdateRow row : rows) {
            err = false;
            row.rowMsg = null;

            if (StringUtils.isEmpty(row.code)) {
                row.appendMsg("网点编码不能为空");
                err = true;
            } else {
                try {
                    String id = dao.findIdByCode(row.code);
                    if (id == null) {
                        row.appendMsg("网点编码不存在");
                        err = true;
                    }
                } catch (Exception e) {
                }
            }

            if (StringUtils.isEmpty(row.name)) {
                row.appendMsg("网点名称不能为空");
                err = true;
            }

            if (err) {
                count++;
            } else {
                // 同一编码的后续数据会覆盖前面的数据（以最后一次为准）
                codeMap.put(row.code, row);
            }
        }

        // 只有当所有行都没有错误时才执行更新
        if (count == 0) {
            // 对每个唯一的网点编码执行更新操作
            for (LocationBatchUpdateRow row : codeMap.values()) {
                dao.updateByCode(row.code, row.name, row.memo);
            }
        }

        return count;
    }
}